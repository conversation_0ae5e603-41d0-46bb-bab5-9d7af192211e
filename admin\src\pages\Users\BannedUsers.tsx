import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
} from '@mui/material';
import {
  Block as BlockIcon,
  CheckCircle as UnblockIcon,
  Visibility as ViewIcon,
  History as HistoryIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminService } from '../../../api/adminService';
import { BannedUser, BanReason, BanType } from '../../../types/admin.types';
import styles from '../../../styles/admin/BannedUsers.module.css';

const BannedUsers: React.FC = () => {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [banTypeFilter, setBanTypeFilter] = useState<BanType | 'all'>('all');
  const [selectedUser, setSelectedUser] = useState<BannedUser | null>(null);
  const [unbanDialogOpen, setUnbanDialogOpen] = useState(false);
  const [unbanReason, setUnbanReason] = useState('');

  // Загрузка заблокированных пользователей
  const { data: bannedUsersData, isLoading } = useQuery({
    queryKey: ['admin', 'banned-users', page, searchTerm, banTypeFilter],
    queryFn: () => adminService.getBannedUsers({
      page,
      limit: 20,
      search: searchTerm,
      banType: banTypeFilter === 'all' ? undefined : banTypeFilter,
    }),
  });

  // Мутация для разблокировки пользователя
  const unbanUserMutation = useMutation({
    mutationFn: ({ userId, reason }: { userId: string; reason: string }) =>
      adminService.unbanUser(userId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'banned-users'] });
      setUnbanDialogOpen(false);
      setSelectedUser(null);
      setUnbanReason('');
    },
  });

  const handleUnbanUser = (user: BannedUser) => {
    setSelectedUser(user);
    setUnbanDialogOpen(true);
  };

  const handleConfirmUnban = () => {
    if (!selectedUser || !unbanReason.trim()) return;

    unbanUserMutation.mutate({
      userId: selectedUser.id,
      reason: unbanReason,
    });
  };

  const getBanTypeText = (banType: BanType) => {
    switch (banType) {
      case 'temporary':
        return 'Временная';
      case 'permanent':
        return 'Постоянная';
      case 'shadow':
        return 'Теневая';
      default:
        return 'Неизвестно';
    }
  };

  const getBanTypeColor = (banType: BanType) => {
    switch (banType) {
      case 'temporary':
        return 'warning';
      case 'permanent':
        return 'error';
      case 'shadow':
        return 'info';
      default:
        return 'default';
    }
  };

  const getBanReasonText = (reason: BanReason) => {
    switch (reason) {
      case 'inappropriate_content':
        return 'Неподходящий контент';
      case 'spam':
        return 'Спам';
      case 'harassment':
        return 'Домогательства';
      case 'fake_profile':
        return 'Фейковый профиль';
      case 'violation_terms':
        return 'Нарушение условий';
      case 'multiple_violations':
        return 'Множественные нарушения';
      case 'security_threat':
        return 'Угроза безопасности';
      case 'other':
        return 'Другое';
      default:
        return 'Неизвестно';
    }
  };

  const isTemporaryBanExpired = (user: BannedUser) => {
    if (user.banType !== 'temporary' || !user.banExpiresAt) return false;
    return new Date(user.banExpiresAt) < new Date();
  };

  const handleExport = () => {
    adminService.exportBannedUsers({
      search: searchTerm,
      banType: banTypeFilter === 'all' ? undefined : banTypeFilter,
    });
  };

  if (isLoading) {
    return <Typography>Загрузка заблокированных пользователей...</Typography>;
  }

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="h4" component="h1">
          Заблокированные пользователи
        </Typography>
        <Button
          startIcon={<ExportIcon />}
          onClick={handleExport}
          variant="outlined"
        >
          Экспорт
        </Button>
      </Box>

      {/* Фильтры */}
      <Card className={styles.filtersCard}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Поиск по имени или email"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon color="action" />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Тип блокировки</InputLabel>
                <Select
                  value={banTypeFilter}
                  onChange={(e) => setBanTypeFilter(e.target.value as BanType | 'all')}
                  label="Тип блокировки"
                >
                  <MenuItem value="all">Все</MenuItem>
                  <MenuItem value="temporary">Временная</MenuItem>
                  <MenuItem value="permanent">Постоянная</MenuItem>
                  <MenuItem value="shadow">Теневая</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={5}>
              <Typography variant="body2" color="text.secondary">
                Найдено: {bannedUsersData?.total || 0} пользователей
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Таблица заблокированных пользователей */}
      <TableContainer component={Paper} className={styles.tableContainer}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Пользователь</TableCell>
              <TableCell>Тип блокировки</TableCell>
              <TableCell>Причина</TableCell>
              <TableCell>Дата блокировки</TableCell>
              <TableCell>Истекает</TableCell>
              <TableCell>Заблокировал</TableCell>
              <TableCell>Действия</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {bannedUsersData?.users.map((user) => (
              <TableRow 
                key={user.id} 
                className={`${styles.userRow} ${isTemporaryBanExpired(user) ? styles.expiredBan : ''}`}
              >
                <TableCell>
                  <Box className={styles.userInfo}>
                    <Avatar src={user.avatar} className={styles.avatar}>
                      {user.firstName[0]}{user.lastName[0]}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2">
                        {user.firstName} {user.lastName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                
                <TableCell>
                  <Chip
                    label={getBanTypeText(user.banType)}
                    color={getBanTypeColor(user.banType) as any}
                    size="small"
                  />
                  {isTemporaryBanExpired(user) && (
                    <Chip
                      label="Истекла"
                      color="success"
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  )}
                </TableCell>
                
                <TableCell>
                  <Tooltip title={user.banReasonDetails || ''}>
                    <Chip
                      label={getBanReasonText(user.banReason)}
                      variant="outlined"
                      size="small"
                    />
                  </Tooltip>
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2">
                    {new Date(user.bannedAt).toLocaleDateString('ru-RU')}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {new Date(user.bannedAt).toLocaleTimeString('ru-RU')}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  {user.banType === 'temporary' && user.banExpiresAt ? (
                    <Typography variant="body2">
                      {new Date(user.banExpiresAt).toLocaleDateString('ru-RU')}
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Никогда
                    </Typography>
                  )}
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2">
                    {user.bannedBy.firstName} {user.bannedBy.lastName}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Box className={styles.actions}>
                    <Tooltip title="Разблокировать">
                      <IconButton
                        size="small"
                        color="success"
                        onClick={() => handleUnbanUser(user)}
                      >
                        <UnblockIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Просмотреть профиль">
                      <IconButton
                        size="small"
                        onClick={() => window.open(`/admin/users/${user.id}`, '_blank')}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="История блокировок">
                      <IconButton
                        size="small"
                        onClick={() => {
                          // Открыть историю блокировок пользователя
                        }}
                      >
                        <HistoryIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Пагинация */}
      {bannedUsersData && bannedUsersData.totalPages > 1 && (
        <Box className={styles.pagination}>
          <Pagination
            count={bannedUsersData.totalPages}
            page={page}
            onChange={(event, value) => setPage(value)}
            color="primary"
          />
        </Box>
      )}

      {/* Диалог разблокировки */}
      <Dialog open={unbanDialogOpen} onClose={() => setUnbanDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Разблокировать пользователя</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Box>
              <Typography variant="body1" gutterBottom>
                Пользователь: {selectedUser.firstName} {selectedUser.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Email: {selectedUser.email}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Заблокирован: {new Date(selectedUser.bannedAt).toLocaleDateString('ru-RU')}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Причина: {getBanReasonText(selectedUser.banReason)}
              </Typography>
              
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Причина разблокировки"
                value={unbanReason}
                onChange={(e) => setUnbanReason(e.target.value)}
                required
                sx={{ mt: 2 }}
                placeholder="Укажите причину разблокировки пользователя"
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUnbanDialogOpen(false)}>
            Отмена
          </Button>
          <Button
            onClick={handleConfirmUnban}
            variant="contained"
            color="success"
            disabled={!unbanReason.trim() || unbanUserMutation.isPending}
          >
            {unbanUserMutation.isPending ? 'Разблокировка...' : 'Разблокировать'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Уведомления об ошибках */}
      {unbanUserMutation.error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          Ошибка при разблокировке: {unbanUserMutation.error.message}
        </Alert>
      )}
    </Box>
  );
};

export default BannedUsers;
