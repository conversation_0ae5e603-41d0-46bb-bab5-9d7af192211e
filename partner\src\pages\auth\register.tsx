import React, { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Container,
  Paper,
  InputAdornment,
  IconButton,
  Divider,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Email as EmailIcon,
  Lock as LockIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { useMutation } from '@tanstack/react-query';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { partnerService } from '../../services/partnerService';
import { VenueType } from '../../types/partner.types';
import styles from '../../styles/auth/Register.module.css';

// Схема валидации
const schema = yup.object({
  // Личные данные
  firstName: yup.string().required('Имя обязательно').min(2, 'Минимум 2 символа'),
  lastName: yup.string().required('Фамилия обязательна').min(2, 'Минимум 2 символа'),
  email: yup.string().email('Некорректный email').required('Email обязателен'),
  phone: yup.string().matches(/^\+7\d{10}$/, 'Формат: +7XXXXXXXXXX').required('Телефон обязателен'),
  password: yup.string().min(8, 'Минимум 8 символов').required('Пароль обязателен'),
  confirmPassword: yup.string()
    .oneOf([yup.ref('password')], 'Пароли не совпадают')
    .required('Подтверждение пароля обязательно'),
  
  // Данные заведения
  venueName: yup.string().required('Название заведения обязательно'),
  venueType: yup.string().required('Тип заведения обязателен'),
  venueAddress: yup.string().required('Адрес заведения обязателен'),
  venueCity: yup.string().required('Город обязателен'),
  venueDescription: yup.string().required('Описание заведения обязательно'),
  
  // Согласия
  agreeTerms: yup.boolean().oneOf([true], 'Необходимо согласие с условиями'),
  agreePrivacy: yup.boolean().oneOf([true], 'Необходимо согласие с политикой конфиденциальности'),
});

interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  venueName: string;
  venueType: VenueType;
  venueAddress: string;
  venueCity: string;
  venueDescription: string;
  agreeTerms: boolean;
  agreePrivacy: boolean;
}

const steps = ['Личные данные', 'Данные заведения', 'Подтверждение'];

const PartnerRegister: React.FC = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    trigger,
    watch,
  } = useForm<RegisterFormData>({
    resolver: yupResolver(schema),
    mode: 'onChange',
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      venueName: '',
      venueType: 'restaurant',
      venueAddress: '',
      venueCity: '',
      venueDescription: '',
      agreeTerms: false,
      agreePrivacy: false,
    },
  });

  const registerMutation = useMutation({
    mutationFn: (data: RegisterFormData) => partnerService.register(data),
    onSuccess: () => {
      router.push('/auth/verify?email=' + encodeURIComponent(watch('email')));
    },
  });

  const handleNext = async () => {
    const fieldsToValidate = getFieldsForStep(activeStep);
    const isStepValid = await trigger(fieldsToValidate);
    
    if (isStepValid) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const getFieldsForStep = (step: number): (keyof RegisterFormData)[] => {
    switch (step) {
      case 0:
        return ['firstName', 'lastName', 'email', 'phone', 'password', 'confirmPassword'];
      case 1:
        return ['venueName', 'venueType', 'venueAddress', 'venueCity', 'venueDescription'];
      case 2:
        return ['agreeTerms', 'agreePrivacy'];
      default:
        return [];
    }
  };

  const onSubmit = (data: RegisterFormData) => {
    registerMutation.mutate(data);
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box className={styles.stepContent}>
            <Typography variant="h6" gutterBottom>
              Личная информация
            </Typography>
            
            <Box className={styles.formRow}>
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Имя"
                    error={!!errors.firstName}
                    helperText={errors.firstName?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
              
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Фамилия"
                    error={!!errors.lastName}
                    helperText={errors.lastName?.message}
                  />
                )}
              />
            </Box>

            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Email"
                  type="email"
                  error={!!errors.email}
                  helperText={errors.email?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />

            <Controller
              name="phone"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Телефон"
                  placeholder="+7XXXXXXXXXX"
                  error={!!errors.phone}
                  helperText={errors.phone?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PhoneIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />

            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Пароль"
                  type={showPassword ? 'text' : 'password'}
                  error={!!errors.password}
                  helperText={errors.password?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockIcon color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />

            <Controller
              name="confirmPassword"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Подтверждение пароля"
                  type={showConfirmPassword ? 'text' : 'password'}
                  error={!!errors.confirmPassword}
                  helperText={errors.confirmPassword?.message}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          edge="end"
                        >
                          {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          </Box>
        );

      case 1:
        return (
          <Box className={styles.stepContent}>
            <Typography variant="h6" gutterBottom>
              Информация о заведении
            </Typography>

            <Controller
              name="venueName"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Название заведения"
                  error={!!errors.venueName}
                  helperText={errors.venueName?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <BusinessIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />

            <Controller
              name="venueType"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.venueType}>
                  <InputLabel>Тип заведения</InputLabel>
                  <Select {...field} label="Тип заведения">
                    <MenuItem value="restaurant">Ресторан</MenuItem>
                    <MenuItem value="cafe">Кафе</MenuItem>
                    <MenuItem value="bar">Бар</MenuItem>
                    <MenuItem value="club">Клуб</MenuItem>
                    <MenuItem value="shopping_center">Торговый центр</MenuItem>
                    <MenuItem value="other">Другое</MenuItem>
                  </Select>
                  {errors.venueType && (
                    <Typography variant="caption" color="error">
                      {errors.venueType.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />

            <Box className={styles.formRow}>
              <Controller
                name="venueCity"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Город"
                    error={!!errors.venueCity}
                    helperText={errors.venueCity?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LocationIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
            </Box>

            <Controller
              name="venueAddress"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Адрес заведения"
                  error={!!errors.venueAddress}
                  helperText={errors.venueAddress?.message}
                />
              )}
            />

            <Controller
              name="venueDescription"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  multiline
                  rows={3}
                  label="Описание заведения"
                  error={!!errors.venueDescription}
                  helperText={errors.venueDescription?.message}
                  placeholder="Расскажите о вашем заведении, его особенностях и атмосфере"
                />
              )}
            />
          </Box>
        );

      case 2:
        return (
          <Box className={styles.stepContent}>
            <Typography variant="h6" gutterBottom>
              Подтверждение регистрации
            </Typography>

            <Alert severity="info" className={styles.infoAlert}>
              Пожалуйста, проверьте введенные данные и подтвердите согласие с условиями использования.
            </Alert>

            <Box className={styles.summarySection}>
              <Typography variant="subtitle2" gutterBottom>
                Личные данные:
              </Typography>
              <Typography variant="body2">
                {watch('firstName')} {watch('lastName')}
              </Typography>
              <Typography variant="body2">
                {watch('email')}
              </Typography>
              <Typography variant="body2">
                {watch('phone')}
              </Typography>
            </Box>

            <Box className={styles.summarySection}>
              <Typography variant="subtitle2" gutterBottom>
                Заведение:
              </Typography>
              <Typography variant="body2">
                {watch('venueName')}
              </Typography>
              <Typography variant="body2">
                {watch('venueAddress')}, {watch('venueCity')}
              </Typography>
            </Box>

            <Divider className={styles.divider} />

            <Box className={styles.agreementsSection}>
              <Controller
                name="agreeTerms"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value} />}
                    label={
                      <Typography variant="body2">
                        Я согласен с{' '}
                        <Link href="/legal/terms" target="_blank" className={styles.link}>
                          Условиями использования
                        </Link>
                      </Typography>
                    }
                  />
                )}
              />
              {errors.agreeTerms && (
                <Typography variant="caption" color="error">
                  {errors.agreeTerms.message}
                </Typography>
              )}

              <Controller
                name="agreePrivacy"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value} />}
                    label={
                      <Typography variant="body2">
                        Я согласен с{' '}
                        <Link href="/legal/privacy" target="_blank" className={styles.link}>
                          Политикой конфиденциальности
                        </Link>
                      </Typography>
                    }
                  />
                )}
              />
              {errors.agreePrivacy && (
                <Typography variant="caption" color="error">
                  {errors.agreePrivacy.message}
                </Typography>
              )}
            </Box>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Head>
        <title>Регистрация партнера - Likes & Love</title>
        <meta name="description" content="Станьте партнером Likes & Love и привлекайте новых клиентов" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Box className={styles.container}>
        <Container maxWidth="md">
          <Paper className={styles.registerCard} elevation={8}>
            <Box className={styles.header}>
              <BusinessIcon className={styles.logo} />
              <Typography variant="h4" component="h1" className={styles.title}>
                Стать партнером
              </Typography>
              <Typography variant="body1" color="text.secondary" className={styles.subtitle}>
                Зарегистрируйте ваше заведение в Likes & Love
              </Typography>
            </Box>

            <Divider className={styles.divider} />

            <CardContent className={styles.content}>
              {registerMutation.error && (
                <Alert severity="error" className={styles.alert}>
                  {registerMutation.error.message || 'Ошибка регистрации. Попробуйте снова.'}
                </Alert>
              )}

              <Stepper activeStep={activeStep} className={styles.stepper}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
                {renderStepContent(activeStep)}

                <Box className={styles.actions}>
                  <Button
                    disabled={activeStep === 0}
                    onClick={handleBack}
                    className={styles.backButton}
                  >
                    Назад
                  </Button>
                  
                  {activeStep === steps.length - 1 ? (
                    <Button
                      type="submit"
                      variant="contained"
                      size="large"
                      disabled={!isValid || registerMutation.isPending}
                      className={styles.submitButton}
                    >
                      {registerMutation.isPending ? 'Регистрация...' : 'Зарегистрироваться'}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleNext}
                      size="large"
                      className={styles.nextButton}
                    >
                      Далее
                    </Button>
                  )}
                </Box>
              </form>
            </CardContent>

            <Box className={styles.footer}>
              <Typography variant="body2" color="text.secondary" align="center">
                Уже есть аккаунт?{' '}
                <Link href="/auth/login" className={styles.loginLink}>
                  Войти
                </Link>
              </Typography>
            </Box>
          </Paper>
        </Container>
      </Box>
    </>
  );
};

export default PartnerRegister;
