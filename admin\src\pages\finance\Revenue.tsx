import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Button,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  CreditCard as CardIcon,
  AccountBalance as BankIcon,
  GetApp as ExportIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { adminService } from '../../../api/adminService';
import { RevenueData, TimeRange, PaymentMethod } from '../../../types/admin.types';
import styles from '../../../styles/admin/Revenue.module.css';

// Регистрируем компоненты Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement
);

const Revenue: React.FC = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');

  // Загрузка данных о доходах
  const { data: revenueData, isLoading, refetch } = useQuery({
    queryKey: ['admin', 'revenue', timeRange],
    queryFn: () => adminService.getRevenueData(timeRange),
  });

  const handleExport = () => {
    adminService.exportRevenueReport(timeRange);
  };

  if (isLoading) {
    return <Typography>Загрузка данных о доходах...</Typography>;
  }

  if (!revenueData) {
    return <Typography>Нет данных для отображения</Typography>;
  }

  // Данные для графика доходов по времени
  const revenueChartData = {
    labels: revenueData.revenueByPeriod.map(item => item.period),
    datasets: [
      {
        label: 'Доходы (₽)',
        data: revenueData.revenueByPeriod.map(item => item.amount),
        borderColor: '#4caf50',
        backgroundColor: 'rgba(76, 175, 80, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  };

  // Данные для графика доходов по способам оплаты
  const paymentMethodsData = {
    labels: revenueData.revenueByPaymentMethod.map(item => item.method),
    datasets: [
      {
        label: 'Доходы по способам оплаты',
        data: revenueData.revenueByPaymentMethod.map(item => item.amount),
        backgroundColor: [
          '#ff6384',
          '#36a2eb',
          '#ffce56',
          '#4bc0c0',
          '#9966ff',
          '#ff9f40',
        ],
      },
    ],
  };

  // Данные для графика доходов по подпискам
  const subscriptionRevenueData = {
    labels: revenueData.revenueBySubscription.map(item => item.type),
    datasets: [
      {
        label: 'Доходы по подпискам (₽)',
        data: revenueData.revenueBySubscription.map(item => item.amount),
        backgroundColor: '#36a2eb',
      },
    ],
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
    }).format(amount);
  };

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case 'card':
        return <CardIcon />;
      case 'bank_transfer':
        return <BankIcon />;
      case 'yookassa':
        return <MoneyIcon />;
      default:
        return <MoneyIcon />;
    }
  };

  const getPaymentMethodName = (method: PaymentMethod) => {
    switch (method) {
      case 'card':
        return 'Банковские карты';
      case 'bank_transfer':
        return 'Банковские переводы';
      case 'yookassa':
        return 'YooKassa';
      case 'stripe':
        return 'Stripe';
      case 'tinkoff':
        return 'Тинькофф';
      case 'sberbank':
        return 'Сбербанк';
      case 'sbp':
        return 'СБП';
      default:
        return method;
    }
  };

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="h4" component="h1">
          Доходы
        </Typography>
        <Box className={styles.controls}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Период</InputLabel>
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as TimeRange)}
              label="Период"
            >
              <MenuItem value="7d">7 дней</MenuItem>
              <MenuItem value="30d">30 дней</MenuItem>
              <MenuItem value="90d">3 месяца</MenuItem>
              <MenuItem value="1y">1 год</MenuItem>
            </Select>
          </FormControl>
          <IconButton onClick={() => refetch()} color="primary">
            <RefreshIcon />
          </IconButton>
          <Button
            startIcon={<ExportIcon />}
            onClick={handleExport}
            variant="outlined"
          >
            Экспорт
          </Button>
        </Box>
      </Box>

      {/* Основные метрики */}
      <Grid container spacing={3} className={styles.metricsGrid}>
        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.metricCard}>
            <CardContent>
              <Box className={styles.metricContent}>
                <MoneyIcon className={styles.metricIcon} color="success" />
                <Box>
                  <Typography variant="h4" component="div">
                    {formatCurrency(revenueData.totalRevenue)}
                  </Typography>
                  <Typography color="text.secondary">
                    Общий доход
                  </Typography>
                  <Box className={styles.trend}>
                    {revenueData.revenueGrowth > 0 ? (
                      <TrendingUpIcon color="success" />
                    ) : (
                      <TrendingDownIcon color="error" />
                    )}
                    <Typography variant="body2" color={revenueData.revenueGrowth > 0 ? 'success.main' : 'error.main'}>
                      {Math.abs(revenueData.revenueGrowth)}%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.metricCard}>
            <CardContent>
              <Box className={styles.metricContent}>
                <CardIcon className={styles.metricIcon} color="primary" />
                <Box>
                  <Typography variant="h4" component="div">
                    {formatCurrency(revenueData.monthlyRevenue)}
                  </Typography>
                  <Typography color="text.secondary">
                    Доход за месяц
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.metricCard}>
            <CardContent>
              <Box className={styles.metricContent}>
                <BankIcon className={styles.metricIcon} color="info" />
                <Box>
                  <Typography variant="h4" component="div">
                    {formatCurrency(revenueData.averageRevenuePerUser)}
                  </Typography>
                  <Typography color="text.secondary">
                    ARPU
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.metricCard}>
            <CardContent>
              <Box className={styles.metricContent}>
                <TrendingUpIcon className={styles.metricIcon} color="warning" />
                <Box>
                  <Typography variant="h4" component="div">
                    {revenueData.totalTransactions.toLocaleString()}
                  </Typography>
                  <Typography color="text.secondary">
                    Всего транзакций
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Графики */}
      <Grid container spacing={3} className={styles.chartsGrid}>
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Динамика доходов
              </Typography>
              <Box className={styles.chartContainer}>
                <Line data={revenueChartData} options={{ responsive: true, maintainAspectRatio: false }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Доходы по способам оплаты
              </Typography>
              <Box className={styles.chartContainer}>
                <Doughnut data={paymentMethodsData} options={{ responsive: true, maintainAspectRatio: false }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Доходы по типам подписок
              </Typography>
              <Box className={styles.chartContainer}>
                <Bar data={subscriptionRevenueData} options={{ responsive: true, maintainAspectRatio: false }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Топ способы оплаты
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Способ оплаты</TableCell>
                      <TableCell align="right">Сумма</TableCell>
                      <TableCell align="right">Транзакции</TableCell>
                      <TableCell align="right">Доля</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {revenueData.revenueByPaymentMethod.map((item) => (
                      <TableRow key={item.method}>
                        <TableCell>
                          <Box className={styles.paymentMethod}>
                            {getPaymentMethodIcon(item.method)}
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              {getPaymentMethodName(item.method)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="bold">
                            {formatCurrency(item.amount)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Chip label={item.transactions} size="small" />
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2">
                            {((item.amount / revenueData.totalRevenue) * 100).toFixed(1)}%
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Детальная таблица доходов */}
      <Card className={styles.detailsCard}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Детализация доходов по периодам
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Период</TableCell>
                  <TableCell align="right">Доходы</TableCell>
                  <TableCell align="right">Транзакции</TableCell>
                  <TableCell align="right">Новые подписки</TableCell>
                  <TableCell align="right">Продления</TableCell>
                  <TableCell align="right">Средний чек</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {revenueData.revenueByPeriod.map((item) => (
                  <TableRow key={item.period}>
                    <TableCell>
                      <Typography variant="body2">
                        {item.period}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="bold" color="success.main">
                        {formatCurrency(item.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Chip label={item.transactions} size="small" color="primary" />
                    </TableCell>
                    <TableCell align="right">
                      <Chip label={item.newSubscriptions} size="small" color="success" />
                    </TableCell>
                    <TableCell align="right">
                      <Chip label={item.renewals} size="small" color="info" />
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {formatCurrency(item.averageTicket)}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Revenue;
