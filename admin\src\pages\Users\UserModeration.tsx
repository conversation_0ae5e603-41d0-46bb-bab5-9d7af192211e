import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Badge,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Block as BlockIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Report as ReportIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminService } from '../../../api/adminService';
import { ModerationAction, UserModerationData, ModerationReason } from '../../../types/admin.types';
import styles from '../../../styles/admin/UserModeration.module.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const UserModeration: React.FC = () => {
  const queryClient = useQueryClient();
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedUser, setSelectedUser] = useState<UserModerationData | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<ModerationAction>('warn');
  const [reason, setReason] = useState<ModerationReason>('inappropriate_content');
  const [customReason, setCustomReason] = useState('');
  const [duration, setDuration] = useState('24'); // часы для временной блокировки

  // Загрузка пользователей для модерации
  const { data: moderationData, isLoading } = useQuery({
    queryKey: ['admin', 'user-moderation'],
    queryFn: () => adminService.getUserModerationData(),
  });

  // Мутация для применения модерационных действий
  const moderationMutation = useMutation({
    mutationFn: ({ userId, action, reason, customReason, duration }: {
      userId: string;
      action: ModerationAction;
      reason: ModerationReason;
      customReason?: string;
      duration?: string;
    }) => adminService.applyModerationAction(userId, action, reason, customReason, duration),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'user-moderation'] });
      setDialogOpen(false);
      setSelectedUser(null);
      setCustomReason('');
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleModerationAction = (user: UserModerationData, action: ModerationAction) => {
    setSelectedUser(user);
    setActionType(action);
    setDialogOpen(true);
  };

  const handleConfirmAction = () => {
    if (!selectedUser) return;

    moderationMutation.mutate({
      userId: selectedUser.id,
      action: actionType,
      reason,
      customReason: reason === 'other' ? customReason : undefined,
      duration: actionType === 'temporary_block' ? duration : undefined,
    });
  };

  const getModerationStatusColor = (status: string) => {
    switch (status) {
      case 'clean':
        return 'success';
      case 'warned':
        return 'warning';
      case 'blocked':
        return 'error';
      case 'under_review':
        return 'info';
      default:
        return 'default';
    }
  };

  const getModerationStatusText = (status: string) => {
    switch (status) {
      case 'clean':
        return 'Чистый';
      case 'warned':
        return 'Предупрежден';
      case 'blocked':
        return 'Заблокирован';
      case 'under_review':
        return 'На проверке';
      default:
        return 'Неизвестно';
    }
  };

  const renderUserRow = (user: UserModerationData) => (
    <TableRow key={user.id} className={styles.userRow}>
      <TableCell>
        <Box className={styles.userInfo}>
          <Avatar src={user.avatar} className={styles.avatar}>
            {user.firstName[0]}{user.lastName[0]}
          </Avatar>
          <Box>
            <Typography variant="subtitle2">
              {user.firstName} {user.lastName}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {user.email}
            </Typography>
          </Box>
        </Box>
      </TableCell>
      
      <TableCell>
        <Chip
          label={getModerationStatusText(user.moderationStatus)}
          color={getModerationStatusColor(user.moderationStatus) as any}
          size="small"
        />
      </TableCell>
      
      <TableCell>
        <Badge badgeContent={user.reportsCount} color="error">
          <ReportIcon />
        </Badge>
      </TableCell>
      
      <TableCell>
        <Typography variant="body2">
          {user.warningsCount}
        </Typography>
      </TableCell>
      
      <TableCell>
        <Typography variant="body2">
          {user.lastActivity ? new Date(user.lastActivity).toLocaleDateString('ru-RU') : 'Никогда'}
        </Typography>
      </TableCell>
      
      <TableCell>
        <Box className={styles.actions}>
          <Tooltip title="Предупреждение">
            <IconButton
              size="small"
              color="warning"
              onClick={() => handleModerationAction(user, 'warn')}
              disabled={user.moderationStatus === 'blocked'}
            >
              <WarningIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Временная блокировка">
            <IconButton
              size="small"
              color="error"
              onClick={() => handleModerationAction(user, 'temporary_block')}
              disabled={user.moderationStatus === 'blocked'}
            >
              <BlockIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Постоянная блокировка">
            <IconButton
              size="small"
              color="error"
              onClick={() => handleModerationAction(user, 'permanent_block')}
              disabled={user.moderationStatus === 'blocked'}
            >
              <SecurityIcon />
            </IconButton>
          </Tooltip>
          
          {user.moderationStatus === 'blocked' && (
            <Tooltip title="Разблокировать">
              <IconButton
                size="small"
                color="success"
                onClick={() => handleModerationAction(user, 'unblock')}
              >
                <CheckIcon />
              </IconButton>
            </Tooltip>
          )}
          
          <Tooltip title="Подробнее">
            <IconButton
              size="small"
              onClick={() => {
                // Открыть детальную информацию о пользователе
                window.open(`/admin/users/${user.id}`, '_blank');
              }}
            >
              <ViewIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </TableCell>
    </TableRow>
  );

  const filterUsersByStatus = (status: string) => {
    return moderationData?.users?.filter(user => user.moderationStatus === status) || [];
  };

  const cleanUsers = filterUsersByStatus('clean');
  const warnedUsers = filterUsersByStatus('warned');
  const blockedUsers = filterUsersByStatus('blocked');
  const underReviewUsers = filterUsersByStatus('under_review');

  if (isLoading) {
    return <Typography>Загрузка...</Typography>;
  }

  return (
    <Box className={styles.container}>
      <Typography variant="h4" component="h1" gutterBottom>
        Модерация пользователей
      </Typography>

      <Paper className={styles.tabsContainer}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab 
            label={
              <Badge badgeContent={cleanUsers.length} color="success">
                Чистые
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={warnedUsers.length} color="warning">
                Предупрежденные
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={blockedUsers.length} color="error">
                Заблокированные
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={underReviewUsers.length} color="info">
                На проверке
              </Badge>
            } 
          />
        </Tabs>
      </Paper>

      <TabPanel value={currentTab} index={0}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Пользователь</TableCell>
                <TableCell>Статус</TableCell>
                <TableCell>Жалобы</TableCell>
                <TableCell>Предупреждения</TableCell>
                <TableCell>Последняя активность</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {cleanUsers.map(renderUserRow)}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={currentTab} index={1}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Пользователь</TableCell>
                <TableCell>Статус</TableCell>
                <TableCell>Жалобы</TableCell>
                <TableCell>Предупреждения</TableCell>
                <TableCell>Последняя активность</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {warnedUsers.map(renderUserRow)}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={currentTab} index={2}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Пользователь</TableCell>
                <TableCell>Статус</TableCell>
                <TableCell>Жалобы</TableCell>
                <TableCell>Предупреждения</TableCell>
                <TableCell>Последняя активность</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {blockedUsers.map(renderUserRow)}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={currentTab} index={3}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Пользователь</TableCell>
                <TableCell>Статус</TableCell>
                <TableCell>Жалобы</TableCell>
                <TableCell>Предупреждения</TableCell>
                <TableCell>Последняя активность</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {underReviewUsers.map(renderUserRow)}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Диалог модерационного действия */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'warn' && 'Предупреждение пользователя'}
          {actionType === 'temporary_block' && 'Временная блокировка'}
          {actionType === 'permanent_block' && 'Постоянная блокировка'}
          {actionType === 'unblock' && 'Разблокировка пользователя'}
        </DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Box>
              <Typography variant="body1" gutterBottom>
                Пользователь: {selectedUser.firstName} {selectedUser.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Email: {selectedUser.email}
              </Typography>
              
              <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
                <InputLabel>Причина</InputLabel>
                <Select
                  value={reason}
                  onChange={(e) => setReason(e.target.value as ModerationReason)}
                  label="Причина"
                >
                  <MenuItem value="inappropriate_content">Неподходящий контент</MenuItem>
                  <MenuItem value="spam">Спам</MenuItem>
                  <MenuItem value="harassment">Домогательства</MenuItem>
                  <MenuItem value="fake_profile">Фейковый профиль</MenuItem>
                  <MenuItem value="violation_terms">Нарушение условий</MenuItem>
                  <MenuItem value="other">Другое</MenuItem>
                </Select>
              </FormControl>

              {reason === 'other' && (
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Укажите причину"
                  value={customReason}
                  onChange={(e) => setCustomReason(e.target.value)}
                  required
                  sx={{ mb: 2 }}
                />
              )}

              {actionType === 'temporary_block' && (
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Длительность блокировки</InputLabel>
                  <Select
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                    label="Длительность блокировки"
                  >
                    <MenuItem value="1">1 час</MenuItem>
                    <MenuItem value="24">24 часа</MenuItem>
                    <MenuItem value="168">7 дней</MenuItem>
                    <MenuItem value="720">30 дней</MenuItem>
                  </Select>
                </FormControl>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Отмена
          </Button>
          <Button
            onClick={handleConfirmAction}
            variant="contained"
            color={actionType === 'unblock' ? 'success' : 'error'}
            disabled={reason === 'other' && !customReason.trim()}
          >
            Применить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserModeration;
