import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  IconButton,
  Tooltip,
  Badge,
  Tabs,
  Tab,
  Paper,
  Zoom,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  Report as ReportIcon,
  Warning as WarningIcon,
  Delete as DeleteIcon,
  ZoomIn as ZoomInIcon,
  Person as PersonIcon,
  Flag as FlagIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminService } from '../../../api/adminService';
import { PhotoModerationItem, ModerationStatus, PhotoModerationReason } from '../../../types/admin.types';
import styles from '../../../styles/admin/PhotoModeration.module.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const PhotoModeration: React.FC = () => {
  const queryClient = useQueryClient();
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedPhoto, setSelectedPhoto] = useState<PhotoModerationItem | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [zoomDialogOpen, setZoomDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');
  const [rejectionReason, setRejectionReason] = useState<PhotoModerationReason>('inappropriate_content');
  const [customReason, setCustomReason] = useState('');

  // Загрузка фотографий для модерации
  const { data: moderationData, isLoading } = useQuery({
    queryKey: ['admin', 'photo-moderation'],
    queryFn: () => adminService.getPhotoModerationQueue(),
  });

  // Мутация для модерации фотографий
  const moderatePhotoMutation = useMutation({
    mutationFn: ({ photoId, action, reason, customReason }: {
      photoId: string;
      action: 'approve' | 'reject';
      reason?: PhotoModerationReason;
      customReason?: string;
    }) => adminService.moderatePhoto(photoId, action, reason, customReason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'photo-moderation'] });
      setDialogOpen(false);
      setSelectedPhoto(null);
      setCustomReason('');
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handlePhotoAction = (photo: PhotoModerationItem, action: 'approve' | 'reject') => {
    setSelectedPhoto(photo);
    setActionType(action);
    setDialogOpen(true);
  };

  const handleConfirmAction = () => {
    if (!selectedPhoto) return;

    moderatePhotoMutation.mutate({
      photoId: selectedPhoto.id,
      action: actionType,
      reason: actionType === 'reject' ? rejectionReason : undefined,
      customReason: rejectionReason === 'other' ? customReason : undefined,
    });
  };

  const handleZoomPhoto = (photo: PhotoModerationItem) => {
    setSelectedPhoto(photo);
    setZoomDialogOpen(true);
  };

  const getStatusColor = (status: ModerationStatus) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      case 'flagged':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: ModerationStatus) => {
    switch (status) {
      case 'pending':
        return 'Ожидает';
      case 'approved':
        return 'Одобрено';
      case 'rejected':
        return 'Отклонено';
      case 'flagged':
        return 'Помечено';
      default:
        return 'Неизвестно';
    }
  };

  const filterPhotosByStatus = (status: ModerationStatus) => {
    return moderationData?.photos?.filter(photo => photo.status === status) || [];
  };

  const renderPhotoCard = (photo: PhotoModerationItem) => (
    <Card key={photo.id} className={styles.photoCard}>
      <Box className={styles.imageContainer}>
        <img
          src={photo.thumbnailUrl}
          alt="Фото для модерации"
          className={styles.photoImage}
          onClick={() => handleZoomPhoto(photo)}
        />
        <Box className={styles.imageOverlay}>
          <IconButton
            className={styles.zoomButton}
            onClick={() => handleZoomPhoto(photo)}
          >
            <ZoomInIcon />
          </IconButton>
          {photo.aiFlags.length > 0 && (
            <Chip
              icon={<WarningIcon />}
              label={`AI: ${photo.aiFlags.length}`}
              color="warning"
              size="small"
              className={styles.aiFlag}
            />
          )}
          {photo.reportsCount > 0 && (
            <Badge badgeContent={photo.reportsCount} color="error">
              <FlagIcon className={styles.reportFlag} />
            </Badge>
          )}
        </Box>
      </Box>
      
      <CardContent className={styles.photoInfo}>
        <Box className={styles.userInfo}>
          <Avatar src={photo.user.avatar} className={styles.userAvatar}>
            {photo.user.firstName[0]}{photo.user.lastName[0]}
          </Avatar>
          <Box>
            <Typography variant="subtitle2">
              {photo.user.firstName} {photo.user.lastName}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {photo.user.email}
            </Typography>
          </Box>
        </Box>

        <Box className={styles.photoDetails}>
          <Typography variant="caption" color="text.secondary">
            Загружено: {new Date(photo.uploadedAt).toLocaleDateString('ru-RU')}
          </Typography>
          <Chip
            label={getStatusText(photo.status)}
            color={getStatusColor(photo.status) as any}
            size="small"
          />
        </Box>

        {photo.aiFlags.length > 0 && (
          <Box className={styles.aiFlags}>
            <Typography variant="caption" color="text.secondary">
              AI предупреждения:
            </Typography>
            {photo.aiFlags.map((flag, index) => (
              <Chip
                key={index}
                label={flag}
                size="small"
                variant="outlined"
                color="warning"
              />
            ))}
          </Box>
        )}

        {photo.status === 'pending' && (
          <Box className={styles.actions}>
            <Button
              variant="contained"
              color="success"
              size="small"
              startIcon={<ApproveIcon />}
              onClick={() => handlePhotoAction(photo, 'approve')}
              fullWidth
              sx={{ mb: 1 }}
            >
              Одобрить
            </Button>
            <Button
              variant="outlined"
              color="error"
              size="small"
              startIcon={<RejectIcon />}
              onClick={() => handlePhotoAction(photo, 'reject')}
              fullWidth
            >
              Отклонить
            </Button>
          </Box>
        )}

        <Button
          variant="text"
          size="small"
          startIcon={<PersonIcon />}
          onClick={() => window.open(`/admin/users/${photo.user.id}`, '_blank')}
          fullWidth
          sx={{ mt: 1 }}
        >
          Профиль пользователя
        </Button>
      </CardContent>
    </Card>
  );

  const pendingPhotos = filterPhotosByStatus('pending');
  const approvedPhotos = filterPhotosByStatus('approved');
  const rejectedPhotos = filterPhotosByStatus('rejected');
  const flaggedPhotos = filterPhotosByStatus('flagged');

  if (isLoading) {
    return <Typography>Загрузка фотографий для модерации...</Typography>;
  }

  return (
    <Box className={styles.container}>
      <Typography variant="h4" component="h1" gutterBottom>
        Модерация фотографий
      </Typography>

      <Paper className={styles.tabsContainer}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab 
            label={
              <Badge badgeContent={pendingPhotos.length} color="warning">
                Ожидают проверки
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={flaggedPhotos.length} color="error">
                Помеченные
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={approvedPhotos.length} color="success">
                Одобренные
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={rejectedPhotos.length} color="error">
                Отклоненные
              </Badge>
            } 
          />
        </Tabs>
      </Paper>

      <TabPanel value={currentTab} index={0}>
        <Grid container spacing={2}>
          {pendingPhotos.length === 0 ? (
            <Grid item xs={12}>
              <Alert severity="info">Нет фотографий, ожидающих модерации</Alert>
            </Grid>
          ) : (
            pendingPhotos.map((photo) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={photo.id}>
                {renderPhotoCard(photo)}
              </Grid>
            ))
          )}
        </Grid>
      </TabPanel>

      <TabPanel value={currentTab} index={1}>
        <Grid container spacing={2}>
          {flaggedPhotos.length === 0 ? (
            <Grid item xs={12}>
              <Alert severity="info">Нет помеченных фотографий</Alert>
            </Grid>
          ) : (
            flaggedPhotos.map((photo) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={photo.id}>
                {renderPhotoCard(photo)}
              </Grid>
            ))
          )}
        </Grid>
      </TabPanel>

      <TabPanel value={currentTab} index={2}>
        <Grid container spacing={2}>
          {approvedPhotos.length === 0 ? (
            <Grid item xs={12}>
              <Alert severity="info">Нет одобренных фотографий</Alert>
            </Grid>
          ) : (
            approvedPhotos.map((photo) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={photo.id}>
                {renderPhotoCard(photo)}
              </Grid>
            ))
          )}
        </Grid>
      </TabPanel>

      <TabPanel value={currentTab} index={3}>
        <Grid container spacing={2}>
          {rejectedPhotos.length === 0 ? (
            <Grid item xs={12}>
              <Alert severity="info">Нет отклоненных фотографий</Alert>
            </Grid>
          ) : (
            rejectedPhotos.map((photo) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={photo.id}>
                {renderPhotoCard(photo)}
              </Grid>
            ))
          )}
        </Grid>
      </TabPanel>

      {/* Диалог подтверждения действия */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'approve' ? 'Одобрить фотографию' : 'Отклонить фотографию'}
        </DialogTitle>
        <DialogContent>
          {selectedPhoto && (
            <Box>
              <Box className={styles.dialogPhotoPreview}>
                <img
                  src={selectedPhoto.thumbnailUrl}
                  alt="Предпросмотр"
                  className={styles.previewImage}
                />
              </Box>
              
              <Typography variant="body1" gutterBottom>
                Пользователь: {selectedPhoto.user.firstName} {selectedPhoto.user.lastName}
              </Typography>
              
              {actionType === 'reject' && (
                <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
                  <InputLabel>Причина отклонения</InputLabel>
                  <Select
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value as PhotoModerationReason)}
                    label="Причина отклонения"
                  >
                    <MenuItem value="inappropriate_content">Неподходящий контент</MenuItem>
                    <MenuItem value="nudity">Обнаженность</MenuItem>
                    <MenuItem value="violence">Насилие</MenuItem>
                    <MenuItem value="fake_photo">Фейковое фото</MenuItem>
                    <MenuItem value="poor_quality">Плохое качество</MenuItem>
                    <MenuItem value="copyright">Нарушение авторских прав</MenuItem>
                    <MenuItem value="other">Другое</MenuItem>
                  </Select>
                </FormControl>
              )}

              {actionType === 'reject' && rejectionReason === 'other' && (
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Укажите причину"
                  value={customReason}
                  onChange={(e) => setCustomReason(e.target.value)}
                  required
                />
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Отмена
          </Button>
          <Button
            onClick={handleConfirmAction}
            variant="contained"
            color={actionType === 'approve' ? 'success' : 'error'}
            disabled={actionType === 'reject' && rejectionReason === 'other' && !customReason.trim()}
          >
            {actionType === 'approve' ? 'Одобрить' : 'Отклонить'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог увеличенного просмотра */}
      <Dialog 
        open={zoomDialogOpen} 
        onClose={() => setZoomDialogOpen(false)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogContent className={styles.zoomDialog}>
          {selectedPhoto && (
            <img
              src={selectedPhoto.originalUrl}
              alt="Увеличенное фото"
              className={styles.zoomedImage}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setZoomDialogOpen(false)}>
            Закрыть
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PhotoModeration;
