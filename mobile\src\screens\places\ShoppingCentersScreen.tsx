import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
  ActivityIndicator,
  Alert,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useQuery } from '@tanstack/react-query';
import { placesService } from '../../services/placesService';
import { ShoppingCenter, PlaceCategory } from '../../types/places.types';
import { SearchBar } from '../../components/SearchBar';
import { FilterModal } from '../../components/FilterModal';
import { PlaceCard } from '../../components/PlaceCard';
import { EmptyState } from '../../components/EmptyState';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { useTheme } from '../../hooks/useTheme';
import { useLocation } from '../../hooks/useLocation';
import { colors, spacing, typography } from '../../styles/theme';

const { width } = Dimensions.get('window');

interface ShoppingCentersScreenProps {}

const ShoppingCentersScreen: React.FC<ShoppingCentersScreenProps> = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { location } = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    priceRange: 'all' as 'budget' | 'mid' | 'premium' | 'all',
    rating: 0,
    distance: 50, // км
    amenities: [] as string[],
  });
  const [refreshing, setRefreshing] = useState(false);

  // Загрузка торговых центров
  const {
    data: shoppingCenters,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['shopping-centers', location?.latitude, location?.longitude, filters, searchQuery],
    queryFn: () => placesService.getShoppingCenters({
      latitude: location?.latitude,
      longitude: location?.longitude,
      search: searchQuery || undefined,
      priceRange: filters.priceRange === 'all' ? undefined : filters.priceRange,
      minRating: filters.rating || undefined,
      radius: filters.distance,
      amenities: filters.amenities.length > 0 ? filters.amenities : undefined,
    }),
    enabled: !!location,
  });

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const handlePlacePress = useCallback((place: ShoppingCenter) => {
    navigation.navigate('PlaceDetail', { 
      placeId: place.id, 
      placeType: 'shopping_center' 
    });
  }, [navigation]);

  const handleAddToFavorites = useCallback(async (placeId: string) => {
    try {
      await placesService.addToFavorites(placeId);
      refetch();
      Alert.alert('Успешно', 'Место добавлено в избранное!');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось добавить в избранное');
    }
  }, [refetch]);

  const handleRemoveFromFavorites = useCallback(async (placeId: string) => {
    try {
      await placesService.removeFromFavorites(placeId);
      refetch();
      Alert.alert('Успешно', 'Место удалено из избранного');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось удалить из избранного');
    }
  }, [refetch]);

  const handleNavigateToPlace = useCallback((place: ShoppingCenter) => {
    navigation.navigate('ARNavigation', {
      destination: {
        latitude: place.latitude,
        longitude: place.longitude,
        name: place.name,
        address: place.address,
      }
    });
  }, [navigation]);

  const handleApplyFilters = useCallback((newFilters: typeof filters) => {
    setFilters(newFilters);
    setFilterModalVisible(false);
  }, []);

  const filteredShoppingCenters = shoppingCenters?.filter(center => {
    if (searchQuery) {
      return center.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
             center.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
             center.brands?.some(brand => 
               brand.toLowerCase().includes(searchQuery.toLowerCase())
             );
    }
    return true;
  }) || [];

  const renderShoppingCenterItem = ({ item }: { item: ShoppingCenter }) => (
    <PlaceCard
      place={item}
      onPress={() => handlePlacePress(item)}
      onFavorite={() => 
        item.isFavorite 
          ? handleRemoveFromFavorites(item.id)
          : handleAddToFavorites(item.id)
      }
      onNavigate={() => handleNavigateToPlace(item)}
      showBrands={true}
      showAmenities={true}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.titleContainer}>
        <Ionicons name="storefront-outline" size={24} color={colors.primary} />
        <Text style={styles.title}>Торговые центры</Text>
      </View>
      
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Поиск торговых центров..."
          style={styles.searchBar}
        />
        
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setFilterModalVisible(true)}
        >
          <Ionicons name="options-outline" size={20} color={colors.primary} />
          {(filters.priceRange !== 'all' || filters.rating > 0 || filters.amenities.length > 0) && (
            <View style={styles.filterBadge} />
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          Найдено: {filteredShoppingCenters.length} торговых центров
        </Text>
        {location && (
          <Text style={styles.statsText}>
            В радиусе {filters.distance} км
          </Text>
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <EmptyState
      icon="storefront-outline"
      title="Торговые центры не найдены"
      description={
        searchQuery
          ? 'По вашему запросу торговых центров не найдено'
          : 'В вашем районе пока нет торговых центров'
      }
      actionText="Сбросить фильтры"
      onAction={() => {
        setSearchQuery('');
        setFilters({
          priceRange: 'all',
          rating: 0,
          distance: 50,
          amenities: [],
        });
      }}
    />
  );

  if (isLoading && !shoppingCenters) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить торговые центры. Проверьте подключение к интернету.
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[colors.primary + '10', colors.background]}
        style={styles.gradient}
      >
        <FlatList
          data={filteredShoppingCenters}
          renderItem={renderShoppingCenterItem}
          keyExtractor={(item) => item.id}
          ListHeaderComponent={renderHeader}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />

        {/* Модальное окно фильтров */}
        <FilterModal
          visible={filterModalVisible}
          onClose={() => setFilterModalVisible(false)}
          filters={filters}
          onApply={handleApplyFilters}
          filterType="shopping_center"
        />
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
    paddingBottom: spacing.md,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  title: {
    ...typography.h1,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  searchBar: {
    flex: 1,
    marginRight: spacing.sm,
  },
  filterButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  filterBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.accent,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statsText: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  listContent: {
    paddingBottom: spacing.xl,
  },
  separator: {
    height: spacing.sm,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    ...typography.h2,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 25,
  },
  retryButtonText: {
    ...typography.button,
    color: colors.white,
  },
});

export default ShoppingCentersScreen;
