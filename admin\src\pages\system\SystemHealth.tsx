import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Button,
} from '@mui/material';
import {
  Memory,
  Storage,
  Speed,
  NetworkCheck,
  Database,
  Cloud,
  Security,
  Refresh,
  Warning,
  CheckCircle,
  Error,
  Info,
  Timeline,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
} from 'recharts';
import { useQuery } from '@tanstack/react-query';
import { adminApi } from '../../api/adminApi';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    load: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    inbound: number;
    outbound: number;
    latency: number;
  };
  database: {
    connections: number;
    maxConnections: number;
    queryTime: number;
    status: 'healthy' | 'warning' | 'error';
  };
  services: Array<{
    name: string;
    status: 'running' | 'stopped' | 'error';
    uptime: number;
    memory: number;
    cpu: number;
  }>;
  performance: Array<{
    timestamp: string;
    cpu: number;
    memory: number;
    responseTime: number;
  }>;
}

const SystemHealth: React.FC = () => {
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Загрузка метрик системы
  const {
    data: metrics,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['admin', 'system', 'health'],
    queryFn: () => adminApi.getSystemHealth(),
    refetchInterval: autoRefresh ? 30000 : false, // Обновление каждые 30 секунд
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
      case 'stopped':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running':
        return <CheckCircle />;
      case 'warning':
        return <Warning />;
      case 'error':
      case 'stopped':
        return <Error />;
      default:
        return <Info />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}д ${hours}ч ${minutes}м`;
    } else if (hours > 0) {
      return `${hours}ч ${minutes}м`;
    } else {
      return `${minutes}м`;
    }
  };

  if (isLoading || !metrics) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Загрузка метрик системы...</Typography>
        <LinearProgress sx={{ mt: 2 }} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Заголовок */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Состояние системы
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Button
            variant={autoRefresh ? 'contained' : 'outlined'}
            onClick={() => setAutoRefresh(!autoRefresh)}
            size="small"
          >
            {autoRefresh ? 'Авто-обновление ВКЛ' : 'Авто-обновление ВЫКЛ'}
          </Button>
          
          <Tooltip title="Обновить данные">
            <IconButton onClick={() => refetch()}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Общий статус */}
      <Alert 
        severity={metrics.database.status === 'healthy' ? 'success' : 'warning'} 
        sx={{ mb: 3 }}
      >
        Система работает {metrics.database.status === 'healthy' ? 'стабильно' : 'с предупреждениями'}
      </Alert>

      {/* Основные метрики */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">CPU</Typography>
                <Speed sx={{ color: 'primary.main' }} />
              </Box>
              
              <Typography variant="h4" gutterBottom>
                {metrics.cpu.usage.toFixed(1)}%
              </Typography>
              
              <LinearProgress
                variant="determinate"
                value={metrics.cpu.usage}
                sx={{ mb: 1 }}
                color={metrics.cpu.usage > 80 ? 'error' : metrics.cpu.usage > 60 ? 'warning' : 'primary'}
              />
              
              <Typography variant="body2" color="textSecondary">
                {metrics.cpu.cores} ядер, Load: {metrics.cpu.load.join(', ')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">Память</Typography>
                <Memory sx={{ color: 'info.main' }} />
              </Box>
              
              <Typography variant="h4" gutterBottom>
                {metrics.memory.percentage.toFixed(1)}%
              </Typography>
              
              <LinearProgress
                variant="determinate"
                value={metrics.memory.percentage}
                sx={{ mb: 1 }}
                color={metrics.memory.percentage > 80 ? 'error' : metrics.memory.percentage > 60 ? 'warning' : 'primary'}
              />
              
              <Typography variant="body2" color="textSecondary">
                {formatBytes(metrics.memory.used)} / {formatBytes(metrics.memory.total)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">Диск</Typography>
                <Storage sx={{ color: 'warning.main' }} />
              </Box>
              
              <Typography variant="h4" gutterBottom>
                {metrics.disk.percentage.toFixed(1)}%
              </Typography>
              
              <LinearProgress
                variant="determinate"
                value={metrics.disk.percentage}
                sx={{ mb: 1 }}
                color={metrics.disk.percentage > 80 ? 'error' : metrics.disk.percentage > 60 ? 'warning' : 'primary'}
              />
              
              <Typography variant="body2" color="textSecondary">
                {formatBytes(metrics.disk.used)} / {formatBytes(metrics.disk.total)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">Сеть</Typography>
                <NetworkCheck sx={{ color: 'success.main' }} />
              </Box>
              
              <Typography variant="h4" gutterBottom>
                {metrics.network.latency}ms
              </Typography>
              
              <Typography variant="body2" color="textSecondary">
                ↓ {formatBytes(metrics.network.inbound)}/s
              </Typography>
              <Typography variant="body2" color="textSecondary">
                ↑ {formatBytes(metrics.network.outbound)}/s
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* База данных */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">База данных</Typography>
                <Database sx={{ color: 'secondary.main' }} />
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Статус
                  </Typography>
                  <Chip
                    icon={getStatusIcon(metrics.database.status)}
                    label={metrics.database.status}
                    color={getStatusColor(metrics.database.status) as any}
                    size="small"
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Соединения
                  </Typography>
                  <Typography variant="body1">
                    {metrics.database.connections} / {metrics.database.maxConnections}
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">
                    Время запроса
                  </Typography>
                  <Typography variant="body1">
                    {metrics.database.queryTime}ms
                  </Typography>
                  
                  <LinearProgress
                    variant="determinate"
                    value={(metrics.database.connections / metrics.database.maxConnections) * 100}
                    sx={{ mt: 1 }}
                    color={
                      (metrics.database.connections / metrics.database.maxConnections) > 0.8 
                        ? 'error' 
                        : (metrics.database.connections / metrics.database.maxConnections) > 0.6 
                        ? 'warning' 
                        : 'primary'
                    }
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Сервисы */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Сервисы
              </Typography>
              
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Сервис</TableCell>
                      <TableCell>Статус</TableCell>
                      <TableCell>Uptime</TableCell>
                      <TableCell>CPU</TableCell>
                      <TableCell>RAM</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {metrics.services.map((service) => (
                      <TableRow key={service.name}>
                        <TableCell>{service.name}</TableCell>
                        <TableCell>
                          <Chip
                            icon={getStatusIcon(service.status)}
                            label={service.status}
                            color={getStatusColor(service.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{formatUptime(service.uptime)}</TableCell>
                        <TableCell>{service.cpu.toFixed(1)}%</TableCell>
                        <TableCell>{formatBytes(service.memory)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* График производительности */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">Производительность за последний час</Typography>
                <Timeline sx={{ color: 'primary.main' }} />
              </Box>
              
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={metrics.performance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis />
                  <RechartsTooltip />
                  <Area
                    type="monotone"
                    dataKey="cpu"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    name="CPU %"
                  />
                  <Area
                    type="monotone"
                    dataKey="memory"
                    stackId="1"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    name="Memory %"
                  />
                  <Area
                    type="monotone"
                    dataKey="responseTime"
                    stackId="2"
                    stroke="#ffc658"
                    fill="#ffc658"
                    name="Response Time (ms)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SystemHealth;
