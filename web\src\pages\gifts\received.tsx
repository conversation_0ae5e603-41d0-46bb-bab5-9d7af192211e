import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  TextField,
  MenuItem,
  Pagination,
  Skeleton,
  Alert,
  Avatar,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Badge,
} from '@mui/material';
import {
  Favorite,
  Visibility,
  Message,
  Person,
  Diamond,
  CheckCircle,
  Schedule,
  Reply,
  Close,
  ThumbUp,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { giftsService } from '../../services/giftsService';
import { ReceivedGift } from '../../types/gifts.types';
import { formatDate, formatDateTime } from '../../utils/dateUtils';
import styles from './received.module.css';

interface ReceivedGiftsProps {}

const ReceivedGifts: React.FC<ReceivedGiftsProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: 'all',
    search: '',
  });
  const [page, setPage] = useState(1);
  const [selectedGift, setSelectedGift] = useState<ReceivedGift | null>(null);
  const [thankYouDialogOpen, setThankYouDialogOpen] = useState(false);
  const [thankYouMessage, setThankYouMessage] = useState('');

  // Загрузка полученных подарков
  const {
    data: receivedGiftsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['gifts', 'received', filters, page],
    queryFn: () => giftsService.getReceivedGifts({ ...filters, page, limit: 12 }),
    enabled: isAuthenticated,
  });

  // Мутация для отметки как просмотренный
  const markAsViewedMutation = useMutation({
    mutationFn: (giftId: string) => giftsService.markGiftAsViewed(giftId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gifts', 'received'] });
    },
  });

  // Мутация для отправки благодарности
  const sendThankYouMutation = useMutation({
    mutationFn: ({ giftId, message }: { giftId: string; message: string }) =>
      giftsService.sendThankYou(giftId, message),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gifts', 'received'] });
      setThankYouDialogOpen(false);
      setThankYouMessage('');
      setSelectedGift(null);
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/gifts/received');
    }
  }, [isAuthenticated, router]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleViewGift = (gift: ReceivedGift) => {
    if (gift.status === 'unread') {
      markAsViewedMutation.mutate(gift.id);
    }
  };

  const handleSendThankYou = (gift: ReceivedGift) => {
    setSelectedGift(gift);
    setThankYouDialogOpen(true);
  };

  const handleConfirmThankYou = () => {
    if (selectedGift) {
      sendThankYouMutation.mutate({
        giftId: selectedGift.id,
        message: thankYouMessage,
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'unread': return 'error';
      case 'viewed': return 'info';
      case 'thanked': return 'success';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'unread': return 'Новый';
      case 'viewed': return 'Просмотрен';
      case 'thanked': return 'Поблагодарен';
      default: return status;
    }
  };

  const statusOptions = [
    { value: 'all', label: 'Все статусы' },
    { value: 'unread', label: 'Новые' },
    { value: 'viewed', label: 'Просмотренные' },
    { value: 'thanked', label: 'Поблагодаренные' },
  ];

  const dateRangeOptions = [
    { value: 'all', label: 'За все время' },
    { value: 'today', label: 'Сегодня' },
    { value: 'week', label: 'За неделю' },
    { value: 'month', label: 'За месяц' },
    { value: 'year', label: 'За год' },
  ];

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Полученные подарки - Likes & Love</title>
        <meta name="description" content="Просмотрите подарки, которые вы получили от других пользователей." />
        <meta name="keywords" content="подарки, полученные, благодарность, романтика" />
        <meta property="og:title" content="Полученные подарки - Likes & Love" />
        <meta property="og:description" content="Просмотрите полученные подарки" />
        <meta property="og:type" content="website" />
      </Head>

      <Container maxWidth="xl" className={styles.container}>
        {/* Заголовок */}
        <Box className={styles.header}>
          <Typography variant="h3" component="h1" className={styles.title}>
            Полученные подарки
          </Typography>
          <Typography variant="h6" color="text.secondary" className={styles.subtitle}>
            Подарки, которые вы получили от других пользователей
          </Typography>
        </Box>

        {/* Фильтры */}
        <Card className={styles.filtersCard}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  placeholder="Поиск по отправителю..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  size="small"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label="Статус"
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  size="small"
                >
                  {statusOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label="Период"
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  size="small"
                >
                  {dateRangeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => router.push('/gifts/catalog')}
                  startIcon={<Favorite />}
                >
                  Подарить в ответ
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Статистика */}
        <Grid container spacing={3} className={styles.statsGrid}>
          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent className={styles.statContent}>
                <Box className={styles.statIcon}>
                  <Favorite color="error" />
                </Box>
                <Typography variant="h4" className={styles.statNumber}>
                  {receivedGiftsData?.stats?.total || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Всего получено
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent className={styles.statContent}>
                <Box className={styles.statIcon}>
                  <Badge badgeContent={receivedGiftsData?.stats?.unread || 0} color="error">
                    <Schedule color="warning" />
                  </Badge>
                </Box>
                <Typography variant="h4" className={styles.statNumber}>
                  {receivedGiftsData?.stats?.unread || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Новых
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent className={styles.statContent}>
                <Box className={styles.statIcon}>
                  <Diamond color="warning" />
                </Box>
                <Typography variant="h4" className={styles.statNumber}>
                  {receivedGiftsData?.stats?.totalValue || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Общая стоимость 💎
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent className={styles.statContent}>
                <Box className={styles.statIcon}>
                  <Person color="info" />
                </Box>
                <Typography variant="h4" className={styles.statNumber}>
                  {receivedGiftsData?.stats?.uniqueSenders || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Отправителей
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Список подарков */}
        {isLoading ? (
          <Grid container spacing={3} className={styles.giftsGrid}>
            {Array.from({ length: 8 }).map((_, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Card>
                  <Skeleton variant="rectangular" height={120} />
                  <CardContent>
                    <Skeleton variant="text" height={24} />
                    <Skeleton variant="text" height={20} />
                    <Skeleton variant="text" height={20} width="60%" />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : error ? (
          <Alert severity="error" className={styles.errorAlert}>
            Ошибка загрузки полученных подарков. Попробуйте обновить страницу.
          </Alert>
        ) : receivedGiftsData?.gifts?.length === 0 ? (
          <Alert severity="info" className={styles.emptyAlert}>
            <Typography variant="h6" gutterBottom>
              У вас пока нет полученных подарков
            </Typography>
            <Typography variant="body2" gutterBottom>
              Когда кто-то отправит вам подарок, он появится здесь.
            </Typography>
            <Button
              variant="contained"
              onClick={() => router.push('/discover')}
              startIcon={<Person />}
              sx={{ mt: 2 }}
            >
              Найти знакомства
            </Button>
          </Alert>
        ) : (
          <>
            <Grid container spacing={3} className={styles.giftsGrid}>
              {receivedGiftsData?.gifts?.map((receivedGift: ReceivedGift) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={receivedGift.id}>
                  <Card 
                    className={`${styles.giftCard} ${receivedGift.status === 'unread' ? styles.unreadGift : ''}`}
                    onClick={() => handleViewGift(receivedGift)}
                  >
                    <Box className={styles.giftHeader}>
                      <img
                        src={receivedGift.gift.image}
                        alt={receivedGift.gift.name}
                        className={styles.giftImage}
                      />
                      <Chip
                        label={getStatusLabel(receivedGift.status)}
                        color={getStatusColor(receivedGift.status) as any}
                        size="small"
                        className={styles.statusChip}
                      />
                      {receivedGift.status === 'unread' && (
                        <Badge
                          color="error"
                          variant="dot"
                          className={styles.newBadge}
                        />
                      )}
                    </Box>

                    <CardContent className={styles.giftContent}>
                      <Typography variant="h6" className={styles.giftName}>
                        {receivedGift.gift.name}
                      </Typography>

                      <Box className={styles.senderInfo}>
                        <Avatar
                          src={receivedGift.sender.avatar}
                          sx={{ width: 32, height: 32 }}
                        >
                          {receivedGift.sender.firstName.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {receivedGift.sender.firstName} {receivedGift.sender.lastName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {receivedGift.sender.age} лет
                          </Typography>
                        </Box>
                      </Box>

                      <Divider sx={{ my: 1 }} />

                      <Box className={styles.giftDetails}>
                        <Box className={styles.giftPrice}>
                          <Diamond sx={{ color: 'warning.main', fontSize: 16 }} />
                          <Typography variant="body2" fontWeight="bold">
                            {receivedGift.gift.price}
                          </Typography>
                        </Box>

                        <Typography variant="caption" color="text.secondary">
                          {formatDateTime(receivedGift.receivedAt)}
                        </Typography>
                      </Box>

                      {receivedGift.message && (
                        <Typography variant="body2" className={styles.giftMessage}>
                          "{receivedGift.message}"
                        </Typography>
                      )}

                      <Box className={styles.giftActions}>
                        <Tooltip title="Просмотреть профиль">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/profile/${receivedGift.sender.id}`);
                            }}
                          >
                            <Visibility />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Написать сообщение">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/chat/${receivedGift.sender.id}`);
                            }}
                          >
                            <Message />
                          </IconButton>
                        </Tooltip>

                        {receivedGift.status !== 'thanked' && (
                          <Tooltip title="Поблагодарить">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSendThankYou(receivedGift);
                              }}
                            >
                              <ThumbUp />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* Пагинация */}
            {receivedGiftsData?.totalPages > 1 && (
              <Box className={styles.paginationContainer}>
                <Pagination
                  count={receivedGiftsData.totalPages}
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                  size="large"
                />
              </Box>
            )}
          </>
        )}

        {/* Диалог благодарности */}
        <Dialog
          open={thankYouDialogOpen}
          onClose={() => setThankYouDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              Поблагодарить за подарок
              <IconButton onClick={() => setThankYouDialogOpen(false)}>
                <Close />
              </IconButton>
            </Box>
          </DialogTitle>
          
          <DialogContent>
            {selectedGift && (
              <Box className={styles.thankYouContent}>
                <Box className={styles.selectedGift}>
                  <img
                    src={selectedGift.gift.image}
                    alt={selectedGift.gift.name}
                    className={styles.selectedGiftImage}
                  />
                  <Box>
                    <Typography variant="h6">{selectedGift.gift.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      от {selectedGift.sender.firstName} {selectedGift.sender.lastName}
                    </Typography>
                  </Box>
                </Box>

                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Сообщение благодарности"
                  placeholder="Напишите сообщение благодарности..."
                  value={thankYouMessage}
                  onChange={(e) => setThankYouMessage(e.target.value)}
                  sx={{ mt: 2 }}
                />
              </Box>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setThankYouDialogOpen(false)}>
              Отмена
            </Button>
            <Button
              variant="contained"
              onClick={handleConfirmThankYou}
              disabled={sendThankYouMutation.isPending}
              startIcon={<ThumbUp />}
            >
              {sendThankYouMutation.isPending ? 'Отправка...' : 'Поблагодарить'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default ReceivedGifts;
