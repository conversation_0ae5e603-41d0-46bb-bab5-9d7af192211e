.container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.tabsContainer {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
}

.requestsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.verificationCard {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.verificationCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 56px;
  height: 56px;
  font-size: 1.2rem;
  font-weight: 600;
}

.documents {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.statusChip {
  margin-top: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .verificationCard {
    margin-bottom: 16px;
  }
  
  .userInfo {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .documents {
    justify-content: center;
  }
  
  .actions {
    margin-top: 16px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .verificationCard {
    background-color: #1e1e1e;
    border-color: #333;
  }
  
  .verificationCard:hover {
    box-shadow: 0 4px 16px rgba(255, 255, 255, 0.1);
  }
}

/* Animation for status changes */
.statusTransition {
  transition: all 0.3s ease;
}

/* Badge styles */
.pendingBadge {
  background-color: #ff9800;
  color: white;
}

.approvedBadge {
  background-color: #4caf50;
  color: white;
}

.rejectedBadge {
  background-color: #f44336;
  color: white;
}

/* Document preview styles */
.documentPreview {
  max-width: 100px;
  max-height: 100px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.documentPreview:hover {
  transform: scale(1.05);
}

/* Loading states */
.loadingCard {
  opacity: 0.6;
  pointer-events: none;
}

.loadingSpinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Empty state */
.emptyState {
  text-align: center;
  padding: 48px 24px;
  color: #666;
}

.emptyStateIcon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Action buttons */
.actionButton {
  min-width: 120px;
  font-weight: 600;
  text-transform: none;
  border-radius: 8px;
}

.approveButton {
  background: linear-gradient(45deg, #4caf50, #66bb6a);
  color: white;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.approveButton:hover {
  background: linear-gradient(45deg, #388e3c, #4caf50);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.rejectButton {
  background: linear-gradient(45deg, #f44336, #ef5350);
  color: white;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.rejectButton:hover {
  background: linear-gradient(45deg, #d32f2f, #f44336);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
}

/* Tab indicators */
.tabIndicator {
  background: linear-gradient(45deg, #2196f3, #21cbf3);
  height: 3px;
  border-radius: 3px;
}

/* Verification details */
.verificationDetails {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.detailRow:last-child {
  border-bottom: none;
}

.detailLabel {
  font-weight: 600;
  color: #666;
}

.detailValue {
  color: #333;
}

/* Success/Error states */
.successMessage {
  background: linear-gradient(45deg, #4caf50, #66bb6a);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.errorMessage {
  background: linear-gradient(45deg, #f44336, #ef5350);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

/* Accessibility */
.visuallyHidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
.actionButton:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .actions {
    display: none;
  }
  
  .verificationCard {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
}
