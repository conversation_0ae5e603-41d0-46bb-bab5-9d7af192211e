import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  TextField,
  MenuItem,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AttachMoney,
  CreditCard,
  AccountBalance,
  Download,
  Refresh,
  DateRange,
  PieChart,
  BarChart,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart as RechartsPieChart,
  Cell,
  BarChart as RechartsBar<PERSON>hart,
  <PERSON>,
  Legend,
} from 'recharts';
import { useQuery } from '@tanstack/react-query';
import { adminApi } from '../../api/adminApi';
import { formatCurrency, formatDate } from '../../utils/formatUtils';

interface RevenueData {
  totalRevenue: number;
  monthlyRevenue: number;
  subscriptionRevenue: number;
  giftRevenue: number;
  premiumRevenue: number;
  revenueGrowth: number;
  subscriptionGrowth: number;
  chartData: Array<{
    date: string;
    revenue: number;
    subscriptions: number;
    gifts: number;
    premium: number;
  }>;
  paymentMethods: Array<{
    method: string;
    amount: number;
    percentage: number;
    color: string;
  }>;
  topUsers: Array<{
    id: string;
    name: string;
    avatar: string;
    totalSpent: number;
    subscriptionType: string;
  }>;
}

const RevenueAnalytics: React.FC = () => {
  const [dateRange, setDateRange] = useState('30d');
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar'>('area');

  // Загрузка данных аналитики доходов
  const {
    data: revenueData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['admin', 'analytics', 'revenue', dateRange],
    queryFn: () => adminApi.getRevenueAnalytics(dateRange),
  });

  const handleExportData = () => {
    // Логика экспорта данных
    console.log('Экспорт данных аналитики');
  };

  const dateRangeOptions = [
    { value: '7d', label: 'Последние 7 дней' },
    { value: '30d', label: 'Последние 30 дней' },
    { value: '90d', label: 'Последние 3 месяца' },
    { value: '1y', label: 'Последний год' },
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (isLoading || !revenueData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Загрузка аналитики доходов...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Заголовок */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Аналитика доходов
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            select
            label="Период"
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            size="small"
            sx={{ minWidth: 200 }}
          >
            {dateRangeOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
          
          <Tooltip title="Обновить данные">
            <IconButton onClick={() => refetch()}>
              <Refresh />
            </IconButton>
          </Tooltip>
          
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={handleExportData}
          >
            Экспорт
          </Button>
        </Box>
      </Box>

      {/* Основные метрики */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Общий доход
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(revenueData.totalRevenue)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {revenueData.revenueGrowth >= 0 ? (
                      <TrendingUp sx={{ color: 'success.main', mr: 0.5 }} />
                    ) : (
                      <TrendingDown sx={{ color: 'error.main', mr: 0.5 }} />
                    )}
                    <Typography
                      variant="body2"
                      color={revenueData.revenueGrowth >= 0 ? 'success.main' : 'error.main'}
                    >
                      {Math.abs(revenueData.revenueGrowth)}%
                    </Typography>
                  </Box>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Подписки
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(revenueData.subscriptionRevenue)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {revenueData.subscriptionGrowth >= 0 ? (
                      <TrendingUp sx={{ color: 'success.main', mr: 0.5 }} />
                    ) : (
                      <TrendingDown sx={{ color: 'error.main', mr: 0.5 }} />
                    )}
                    <Typography
                      variant="body2"
                      color={revenueData.subscriptionGrowth >= 0 ? 'success.main' : 'error.main'}
                    >
                      {Math.abs(revenueData.subscriptionGrowth)}%
                    </Typography>
                  </Box>
                </Box>
                <CreditCard sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Подарки
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(revenueData.giftRevenue)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                    {((revenueData.giftRevenue / revenueData.totalRevenue) * 100).toFixed(1)}% от общего
                  </Typography>
                </Box>
                <Box sx={{ fontSize: 40, color: 'warning.main' }}>🎁</Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Premium
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(revenueData.premiumRevenue)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                    {((revenueData.premiumRevenue / revenueData.totalRevenue) * 100).toFixed(1)}% от общего
                  </Typography>
                </Box>
                <Box sx={{ fontSize: 40, color: 'secondary.main' }}>⭐</Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* График доходов */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Динамика доходов
                </Typography>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    variant={chartType === 'line' ? 'contained' : 'outlined'}
                    onClick={() => setChartType('line')}
                  >
                    Линия
                  </Button>
                  <Button
                    size="small"
                    variant={chartType === 'area' ? 'contained' : 'outlined'}
                    onClick={() => setChartType('area')}
                  >
                    Область
                  </Button>
                  <Button
                    size="small"
                    variant={chartType === 'bar' ? 'contained' : 'outlined'}
                    onClick={() => setChartType('bar')}
                  >
                    Столбцы
                  </Button>
                </Box>
              </Box>
              
              <ResponsiveContainer width="100%" height={400}>
                {chartType === 'line' && (
                  <LineChart data={revenueData.chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" stroke="#8884d8" name="Общий доход" />
                    <Line type="monotone" dataKey="subscriptions" stroke="#82ca9d" name="Подписки" />
                    <Line type="monotone" dataKey="gifts" stroke="#ffc658" name="Подарки" />
                    <Line type="monotone" dataKey="premium" stroke="#ff7300" name="Premium" />
                  </LineChart>
                )}
                
                {chartType === 'area' && (
                  <AreaChart data={revenueData.chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Area type="monotone" dataKey="revenue" stackId="1" stroke="#8884d8" fill="#8884d8" name="Общий доход" />
                    <Area type="monotone" dataKey="subscriptions" stackId="1" stroke="#82ca9d" fill="#82ca9d" name="Подписки" />
                    <Area type="monotone" dataKey="gifts" stackId="1" stroke="#ffc658" fill="#ffc658" name="Подарки" />
                    <Area type="monotone" dataKey="premium" stackId="1" stroke="#ff7300" fill="#ff7300" name="Premium" />
                  </AreaChart>
                )}
                
                {chartType === 'bar' && (
                  <RechartsBarChart data={revenueData.chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Bar dataKey="subscriptions" fill="#82ca9d" name="Подписки" />
                    <Bar dataKey="gifts" fill="#ffc658" name="Подарки" />
                    <Bar dataKey="premium" fill="#ff7300" name="Premium" />
                  </RechartsBarChart>
                )}
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Способы оплаты */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Способы оплаты
              </Typography>
              
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <RechartsTooltip formatter={(value) => formatCurrency(value as number)} />
                  <RechartsPieChart data={revenueData.paymentMethods}>
                    {revenueData.paymentMethods.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </RechartsPieChart>
                </RechartsPieChart>
              </ResponsiveContainer>
              
              <Box sx={{ mt: 2 }}>
                {revenueData.paymentMethods.map((method, index) => (
                  <Box key={method.method} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box
                        sx={{
                          width: 12,
                          height: 12,
                          backgroundColor: method.color,
                          borderRadius: '50%',
                          mr: 1,
                        }}
                      />
                      <Typography variant="body2">
                        {method.method}
                      </Typography>
                    </Box>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(method.amount)}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {method.percentage}%
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Топ пользователей по тратам */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Топ пользователей по тратам
              </Typography>
              
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Пользователь</TableCell>
                      <TableCell>Общая сумма</TableCell>
                      <TableCell>Подписка</TableCell>
                      <TableCell>Статус</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {revenueData.topUsers.map((user, index) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box
                              component="img"
                              src={user.avatar}
                              sx={{ width: 40, height: 40, borderRadius: '50%', mr: 2 }}
                            />
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {user.name}
                              </Typography>
                              <Typography variant="caption" color="textSecondary">
                                #{index + 1}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {formatCurrency(user.totalSpent)}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2">
                            {user.subscriptionType}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          <Chip
                            label="Premium"
                            color="warning"
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RevenueAnalytics;
