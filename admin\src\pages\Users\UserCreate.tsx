import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  <PERSON>vider,
  <PERSON>ert,
  FormControlLabel,
  Switch,
  TextField,
  MenuItem,
  Button,
  Avatar,
  IconButton,
  Snackbar,
} from '@mui/material';
import {
  PhotoCamera,
  Visibility,
  VisibilityOff,
  Person,
  Email,
  Phone,
  LocationOn,
  Cake,
  Work,
  School,
  Favorite,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { adminApi } from '../../api/adminApi';

// Validation schema
const userCreateSchema = yup.object().shape({
  email: yup.string().email('Неверный формат email').required('Email обязателен'),
  password: yup.string().min(8, 'Минимум 8 символов').required('Пароль обязателен'),
  firstName: yup.string().required('Имя обязательно').min(2, 'Минимум 2 символа'),
  lastName: yup.string().required('Фамилия обязательна').min(2, 'Минимум 2 символа'),
  phone: yup.string().matches(/^\+?[1-9]\d{1,14}$/, 'Неверный формат телефона'),
  birthDate: yup.date().required('Дата рождения обязательна').max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Минимальный возраст 18 лет'),
  gender: yup.string().oneOf(['male', 'female', 'other'], 'Выберите пол').required('Пол обязателен'),
  location: yup.string().required('Местоположение обязательно'),
});

interface UserCreateData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  birthDate: Date;
  gender: 'male' | 'female' | 'other';
  location: string;
  bio?: string;
  occupation?: string;
  education?: string;
  interests?: string[];
  isVerified?: boolean;
  isPremium?: boolean;
  isActive?: boolean;
  role?: 'user' | 'moderator' | 'admin';
}

const UserCreate: React.FC = () => {
  const [notification, setNotification] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<UserCreateData>({
    resolver: yupResolver(userCreateSchema),
    defaultValues: {
      isVerified: false,
      isPremium: false,
      isActive: true,
      role: 'user',
      interests: [],
    },
  });

  const watchedGender = watch('gender');
  const watchedRole = watch('role');

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: UserCreateData) => {
    setIsLoading(true);
    try {
      const response = await adminApi.createUser(data);
      setNotification({ message: 'Пользователь успешно создан', type: 'success' });
      // redirect('/users');
    } catch (error: any) {
      setNotification({ message: error.message || 'Ошибка при создании пользователя', type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const genderOptions = [
    { value: 'male', label: 'Мужской' },
    { value: 'female', label: 'Женский' },
    { value: 'other', label: 'Другой' },
  ];

  const roleOptions = [
    { value: 'user', label: 'Пользователь' },
    { value: 'moderator', label: 'Модератор' },
    { value: 'admin', label: 'Администратор' },
  ];

  const interestOptions = [
    'Спорт', 'Музыка', 'Кино', 'Путешествия', 'Кулинария', 'Чтение',
    'Искусство', 'Технологии', 'Фотография', 'Танцы', 'Игры', 'Природа'
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Создание нового пользователя
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        Создайте новый аккаунт пользователя. Все обязательные поля должны быть заполнены.
      </Alert>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={3}>
          {/* Основная информация */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Основная информация
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="firstName"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Имя"
                          fullWidth
                          error={!!errors.firstName}
                          helperText={errors.firstName?.message}
                          required
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="lastName"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Фамилия"
                          fullWidth
                          error={!!errors.lastName}
                          helperText={errors.lastName?.message}
                          required
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="email"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Email"
                          type="email"
                          fullWidth
                          error={!!errors.email}
                          helperText={errors.email?.message}
                          required
                          InputProps={{
                            startAdornment: <Email sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="password"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Пароль"
                          type={showPassword ? 'text' : 'password'}
                          fullWidth
                          error={!!errors.password}
                          helperText={errors.password?.message}
                          required
                          InputProps={{
                            endAdornment: (
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            ),
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="phone"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Телефон"
                          fullWidth
                          error={!!errors.phone}
                          helperText={errors.phone?.message}
                          InputProps={{
                            startAdornment: <Phone sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="birthDate"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Дата рождения"
                          type="date"
                          fullWidth
                          error={!!errors.birthDate}
                          helperText={errors.birthDate?.message}
                          required
                          InputLabelProps={{ shrink: true }}
                          InputProps={{
                            startAdornment: <Cake sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="gender"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          select
                          label="Пол"
                          fullWidth
                          error={!!errors.gender}
                          helperText={errors.gender?.message}
                          required
                        >
                          {genderOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </TextField>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="location"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Местоположение"
                          fullWidth
                          error={!!errors.location}
                          helperText={errors.location?.message}
                          required
                          InputProps={{
                            startAdornment: <LocationOn sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="bio"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="О себе"
                          multiline
                          rows={3}
                          fullWidth
                          placeholder="Расскажите о пользователе..."
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Фото профиля и настройки */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Фото профиля
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    src={avatarPreview || undefined}
                    sx={{ width: 120, height: 120, mb: 2 }}
                  >
                    <Person sx={{ fontSize: 60 }} />
                  </Avatar>

                  <input
                    accept="image/*"
                    style={{ display: 'none' }}
                    id="avatar-upload"
                    type="file"
                    onChange={handleAvatarChange}
                  />
                  <label htmlFor="avatar-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<PhotoCamera />}
                    >
                      Загрузить фото
                    </Button>
                  </label>
                </Box>

                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Настройки аккаунта
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Controller
                  name="role"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      label="Роль"
                      fullWidth
                      sx={{ mb: 2 }}
                    >
                      {roleOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </TextField>
                  )}
                />

                <Controller
                  name="isVerified"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label="Верифицированный"
                      sx={{ mb: 1, display: 'block' }}
                    />
                  )}
                />

                <Controller
                  name="isPremium"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label="Premium аккаунт"
                      sx={{ mb: 1, display: 'block' }}
                    />
                  )}
                />

                <Controller
                  name="isActive"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label="Активный"
                      sx={{ mb: 1, display: 'block' }}
                    />
                  )}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Дополнительная информация */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Дополнительная информация
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="occupation"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Профессия"
                          fullWidth
                          InputProps={{
                            startAdornment: <Work sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="education"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Образование"
                          fullWidth
                          InputProps={{
                            startAdornment: <School sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Кнопки действий */}
        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={isLoading}
            startIcon={<Person />}
          >
            {isLoading ? 'Создание...' : 'Создать пользователя'}
          </Button>

          <Button
            variant="outlined"
            size="large"
            onClick={() => window.history.back()}
          >
            Отмена
          </Button>
        </Box>
      </form>

      {/* Уведомления */}
      <Snackbar
        open={!!notification}
        autoHideDuration={6000}
        onClose={() => setNotification(null)}
        message={notification?.message}
      />
    </Box>
  );
};

export default UserCreate;
