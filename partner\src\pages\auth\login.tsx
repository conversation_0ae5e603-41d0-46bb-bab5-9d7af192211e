import React, { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Container,
  Paper,
  InputAdornment,
  IconButton,
  Divider,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Email as EmailIcon,
  Lock as LockIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { useMutation } from '@tanstack/react-query';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { partnerService } from '../../services/partnerService';
import { usePartnerAuth } from '../../hooks/usePartnerAuth';
import styles from '../../styles/auth/Login.module.css';

// Схема валидации
const schema = yup.object({
  email: yup.string().email('Некорректный email').required('Email обязателен'),
  password: yup.string().min(6, 'Минимум 6 символов').required('Пароль обязателен'),
  rememberMe: yup.boolean(),
});

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

const PartnerLogin: React.FC = () => {
  const router = useRouter();
  const { login } = usePartnerAuth();
  const [showPassword, setShowPassword] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const loginMutation = useMutation({
    mutationFn: (data: LoginFormData) => partnerService.login(data),
    onSuccess: (response) => {
      login(response.token, response.partner);
      router.push('/dashboard');
    },
  });

  const onSubmit = (data: LoginFormData) => {
    loginMutation.mutate(data);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <>
      <Head>
        <title>Вход в партнерский портал - Likes & Love</title>
        <meta name="description" content="Войдите в партнерский портал Likes & Love для управления вашим заведением" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Box className={styles.container}>
        <Container maxWidth="sm">
          <Paper className={styles.loginCard} elevation={8}>
            <Box className={styles.header}>
              <BusinessIcon className={styles.logo} />
              <Typography variant="h4" component="h1" className={styles.title}>
                Партнерский портал
              </Typography>
              <Typography variant="body1" color="text.secondary" className={styles.subtitle}>
                Войдите в свой аккаунт для управления заведением
              </Typography>
            </Box>

            <Divider className={styles.divider} />

            <CardContent className={styles.content}>
              {loginMutation.error && (
                <Alert severity="error" className={styles.alert}>
                  {loginMutation.error.message || 'Ошибка входа. Проверьте данные и попробуйте снова.'}
                </Alert>
              )}

              <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
                <Controller
                  name="email"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Email"
                      type="email"
                      error={!!errors.email}
                      helperText={errors.email?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                      className={styles.textField}
                      autoComplete="email"
                      autoFocus
                    />
                  )}
                />

                <Controller
                  name="password"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Пароль"
                      type={showPassword ? 'text' : 'password'}
                      error={!!errors.password}
                      helperText={errors.password?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LockIcon color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={togglePasswordVisibility}
                              edge="end"
                              aria-label="toggle password visibility"
                            >
                              {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      className={styles.textField}
                      autoComplete="current-password"
                    />
                  )}
                />

                <Box className={styles.options}>
                  <Controller
                    name="rememberMe"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={<Checkbox {...field} checked={field.value} />}
                        label="Запомнить меня"
                        className={styles.checkbox}
                      />
                    )}
                  />

                  <Link href="/auth/forgot-password" className={styles.forgotLink}>
                    Забыли пароль?
                  </Link>
                </Box>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loginMutation.isPending}
                  className={styles.submitButton}
                >
                  {loginMutation.isPending ? 'Вход...' : 'Войти'}
                </Button>
              </form>

              <Divider className={styles.divider}>
                <Typography variant="body2" color="text.secondary">
                  или
                </Typography>
              </Divider>

              <Box className={styles.registerSection}>
                <Typography variant="body2" color="text.secondary" align="center">
                  Нет аккаунта партнера?
                </Typography>
                <Link href="/auth/register" className={styles.registerLink}>
                  <Button
                    fullWidth
                    variant="outlined"
                    size="large"
                    className={styles.registerButton}
                  >
                    Стать партнером
                  </Button>
                </Link>
              </Box>
            </CardContent>

            <Box className={styles.footer}>
              <Typography variant="caption" color="text.secondary" align="center">
                Входя в систему, вы соглашаетесь с{' '}
                <Link href="/legal/terms" className={styles.footerLink}>
                  Условиями использования
                </Link>{' '}
                и{' '}
                <Link href="/legal/privacy" className={styles.footerLink}>
                  Политикой конфиденциальности
                </Link>
              </Typography>
            </Box>
          </Paper>

          <Box className={styles.helpSection}>
            <Typography variant="body2" color="text.secondary" align="center">
              Нужна помощь?{' '}
              <Link href="/help/contact" className={styles.helpLink}>
                Свяжитесь с поддержкой
              </Link>
            </Typography>
          </Box>
        </Container>
      </Box>
    </>
  );
};

export default PartnerLogin;
