import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Switch,
  FormControlLabel,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  Notifications as NotificationIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminService } from '../../../api/adminService';
import { MarketingCampaign, CampaignType, CampaignStatus, CreateCampaignData } from '../../../types/admin.types';
import styles from '../../../styles/admin/Campaigns.module.css';

const Campaigns: React.FC = () => {
  const queryClient = useQueryClient();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<MarketingCampaign | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<CreateCampaignData>({
    name: '',
    description: '',
    type: 'email',
    targetAudience: 'all_users',
    content: {
      subject: '',
      body: '',
      template: '',
    },
    schedule: {
      startDate: '',
      endDate: '',
      frequency: 'once',
    },
    budget: 0,
    isActive: true,
  });

  // Загрузка кампаний
  const { data: campaigns, isLoading } = useQuery({
    queryKey: ['admin', 'marketing-campaigns'],
    queryFn: () => adminService.getMarketingCampaigns(),
  });

  // Мутация для создания/обновления кампании
  const saveCampaignMutation = useMutation({
    mutationFn: (data: CreateCampaignData & { id?: string }) =>
      data.id ? adminService.updateCampaign(data.id, data) : adminService.createCampaign(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'marketing-campaigns'] });
      setDialogOpen(false);
      resetForm();
    },
  });

  // Мутация для управления кампанией
  const manageCampaignMutation = useMutation({
    mutationFn: ({ campaignId, action }: { campaignId: string; action: 'start' | 'pause' | 'stop' | 'delete' }) =>
      adminService.manageCampaign(campaignId, action),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'marketing-campaigns'] });
    },
  });

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: 'email',
      targetAudience: 'all_users',
      content: {
        subject: '',
        body: '',
        template: '',
      },
      schedule: {
        startDate: '',
        endDate: '',
        frequency: 'once',
      },
      budget: 0,
      isActive: true,
    });
    setSelectedCampaign(null);
    setIsEditing(false);
  };

  const handleCreateCampaign = () => {
    resetForm();
    setDialogOpen(true);
  };

  const handleEditCampaign = (campaign: MarketingCampaign) => {
    setSelectedCampaign(campaign);
    setFormData({
      name: campaign.name,
      description: campaign.description,
      type: campaign.type,
      targetAudience: campaign.targetAudience,
      content: campaign.content,
      schedule: campaign.schedule,
      budget: campaign.budget,
      isActive: campaign.isActive,
    });
    setIsEditing(true);
    setDialogOpen(true);
  };

  const handleSaveCampaign = () => {
    const dataToSave = isEditing && selectedCampaign 
      ? { ...formData, id: selectedCampaign.id }
      : formData;
    
    saveCampaignMutation.mutate(dataToSave);
  };

  const handleCampaignAction = (campaignId: string, action: 'start' | 'pause' | 'stop' | 'delete') => {
    manageCampaignMutation.mutate({ campaignId, action });
  };

  const getCampaignTypeIcon = (type: CampaignType) => {
    switch (type) {
      case 'email':
        return <EmailIcon />;
      case 'sms':
        return <SmsIcon />;
      case 'push':
        return <NotificationIcon />;
      default:
        return <EmailIcon />;
    }
  };

  const getCampaignTypeName = (type: CampaignType) => {
    switch (type) {
      case 'email':
        return 'Email';
      case 'sms':
        return 'SMS';
      case 'push':
        return 'Push-уведомления';
      default:
        return type;
    }
  };

  const getStatusColor = (status: CampaignStatus) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'paused':
        return 'warning';
      case 'completed':
        return 'info';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: CampaignStatus) => {
    switch (status) {
      case 'active':
        return 'Активна';
      case 'paused':
        return 'Приостановлена';
      case 'completed':
        return 'Завершена';
      case 'draft':
        return 'Черновик';
      default:
        return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
    }).format(amount);
  };

  if (isLoading) {
    return <Typography>Загрузка кампаний...</Typography>;
  }

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="h4" component="h1">
          Маркетинговые кампании
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateCampaign}
        >
          Создать кампанию
        </Button>
      </Box>

      {/* Статистика кампаний */}
      <Grid container spacing={3} className={styles.statsGrid}>
        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.statCard}>
            <CardContent>
              <Box className={styles.statContent}>
                <TrendingUpIcon className={styles.statIcon} color="primary" />
                <Box>
                  <Typography variant="h4" component="div">
                    {campaigns?.filter(c => c.status === 'active').length || 0}
                  </Typography>
                  <Typography color="text.secondary">
                    Активных кампаний
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.statCard}>
            <CardContent>
              <Box className={styles.statContent}>
                <PeopleIcon className={styles.statIcon} color="success" />
                <Box>
                  <Typography variant="h4" component="div">
                    {campaigns?.reduce((sum, c) => sum + (c.metrics?.reached || 0), 0).toLocaleString() || 0}
                  </Typography>
                  <Typography color="text.secondary">
                    Охват
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.statCard}>
            <CardContent>
              <Box className={styles.statContent}>
                <EmailIcon className={styles.statIcon} color="info" />
                <Box>
                  <Typography variant="h4" component="div">
                    {campaigns?.reduce((sum, c) => sum + (c.metrics?.conversions || 0), 0) || 0}
                  </Typography>
                  <Typography color="text.secondary">
                    Конверсии
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.statCard}>
            <CardContent>
              <Box className={styles.statContent}>
                <TrendingUpIcon className={styles.statIcon} color="warning" />
                <Box>
                  <Typography variant="h4" component="div">
                    {formatCurrency(campaigns?.reduce((sum, c) => sum + c.budget, 0) || 0)}
                  </Typography>
                  <Typography color="text.secondary">
                    Общий бюджет
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Таблица кампаний */}
      <TableContainer component={Paper} className={styles.tableContainer}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Название</TableCell>
              <TableCell>Тип</TableCell>
              <TableCell>Статус</TableCell>
              <TableCell>Аудитория</TableCell>
              <TableCell>Охват</TableCell>
              <TableCell>Конверсии</TableCell>
              <TableCell>Бюджет</TableCell>
              <TableCell>Действия</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {campaigns?.map((campaign) => (
              <TableRow key={campaign.id} className={styles.campaignRow}>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2">
                      {campaign.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {campaign.description}
                    </Typography>
                  </Box>
                </TableCell>
                
                <TableCell>
                  <Box className={styles.campaignType}>
                    {getCampaignTypeIcon(campaign.type)}
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      {getCampaignTypeName(campaign.type)}
                    </Typography>
                  </Box>
                </TableCell>
                
                <TableCell>
                  <Chip
                    label={getStatusText(campaign.status)}
                    color={getStatusColor(campaign.status) as any}
                    size="small"
                  />
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2">
                    {campaign.targetAudience === 'all_users' ? 'Все пользователи' : 
                     campaign.targetAudience === 'premium_users' ? 'Премиум пользователи' :
                     campaign.targetAudience === 'inactive_users' ? 'Неактивные пользователи' :
                     'Пользовательская'}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Box>
                    <Typography variant="body2">
                      {campaign.metrics?.reached?.toLocaleString() || 0}
                    </Typography>
                    {campaign.metrics?.totalTarget && (
                      <LinearProgress
                        variant="determinate"
                        value={(campaign.metrics.reached / campaign.metrics.totalTarget) * 100}
                        className={styles.progressBar}
                      />
                    )}
                  </Box>
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2">
                    {campaign.metrics?.conversions || 0}
                  </Typography>
                  {campaign.metrics?.conversionRate && (
                    <Typography variant="caption" color="text.secondary">
                      ({campaign.metrics.conversionRate}%)
                    </Typography>
                  )}
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {formatCurrency(campaign.budget)}
                  </Typography>
                  {campaign.metrics?.spent && (
                    <Typography variant="caption" color="text.secondary">
                      Потрачено: {formatCurrency(campaign.metrics.spent)}
                    </Typography>
                  )}
                </TableCell>
                
                <TableCell>
                  <Box className={styles.actions}>
                    {campaign.status === 'draft' && (
                      <Tooltip title="Запустить">
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => handleCampaignAction(campaign.id, 'start')}
                        >
                          <PlayIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    
                    {campaign.status === 'active' && (
                      <Tooltip title="Приостановить">
                        <IconButton
                          size="small"
                          color="warning"
                          onClick={() => handleCampaignAction(campaign.id, 'pause')}
                        >
                          <PauseIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    
                    <Tooltip title="Редактировать">
                      <IconButton
                        size="small"
                        onClick={() => handleEditCampaign(campaign)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Просмотреть детали">
                      <IconButton
                        size="small"
                        onClick={() => {
                          // Открыть детальную страницу кампании
                        }}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Удалить">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleCampaignAction(campaign.id, 'delete')}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Диалог создания/редактирования кампании */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {isEditing ? 'Редактировать кампанию' : 'Создать кампанию'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Название кампании"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Тип кампании</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as CampaignType })}
                  label="Тип кампании"
                >
                  <MenuItem value="email">Email</MenuItem>
                  <MenuItem value="sms">SMS</MenuItem>
                  <MenuItem value="push">Push-уведомления</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Описание"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Целевая аудитория</InputLabel>
                <Select
                  value={formData.targetAudience}
                  onChange={(e) => setFormData({ ...formData, targetAudience: e.target.value })}
                  label="Целевая аудитория"
                >
                  <MenuItem value="all_users">Все пользователи</MenuItem>
                  <MenuItem value="premium_users">Премиум пользователи</MenuItem>
                  <MenuItem value="inactive_users">Неактивные пользователи</MenuItem>
                  <MenuItem value="new_users">Новые пользователи</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Бюджет (₽)"
                value={formData.budget}
                onChange={(e) => setFormData({ ...formData, budget: Number(e.target.value) })}
              />
            </Grid>
            
            {formData.type === 'email' && (
              <>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Тема письма"
                    value={formData.content.subject}
                    onChange={(e) => setFormData({
                      ...formData,
                      content: { ...formData.content, subject: e.target.value }
                    })}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Содержание письма"
                    value={formData.content.body}
                    onChange={(e) => setFormData({
                      ...formData,
                      content: { ...formData.content, body: e.target.value }
                    })}
                  />
                </Grid>
              </>
            )}
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="datetime-local"
                label="Дата начала"
                value={formData.schedule.startDate}
                onChange={(e) => setFormData({
                  ...formData,
                  schedule: { ...formData.schedule, startDate: e.target.value }
                })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="datetime-local"
                label="Дата окончания"
                value={formData.schedule.endDate}
                onChange={(e) => setFormData({
                  ...formData,
                  schedule: { ...formData.schedule, endDate: e.target.value }
                })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                  />
                }
                label="Активная кампания"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Отмена
          </Button>
          <Button
            onClick={handleSaveCampaign}
            variant="contained"
            disabled={!formData.name.trim() || saveCampaignMutation.isPending}
          >
            {saveCampaignMutation.isPending ? 'Сохранение...' : 'Сохранить'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Уведомления об ошибках */}
      {saveCampaignMutation.error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          Ошибка при сохранении: {saveCampaignMutation.error.message}
        </Alert>
      )}
      
      {manageCampaignMutation.error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          Ошибка при управлении кампанией: {manageCampaignMutation.error.message}
        </Alert>
      )}
    </Box>
  );
};

export default Campaigns;
