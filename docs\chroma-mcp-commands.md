# Chroma MCP Server Commands Reference

## Overview
Chroma MCP server предоставляет 12 команд для работы с векторной базой данных. Используется для хранения информации о разработке проекта знакомств Likes & Love.

## Available Tools (12)

### Collection Management

#### chroma_create_collection
Create a new Chroma collection with configurable HNSW parameters.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to create
- `embedding_function_name` (string, optional) - Name of the embedding function to use. Options: 'default', 'cohere', 'openai', 'jina', 'voyageai', 'ollama', 'roboflow'
- `metadata` (string, optional) - Optional metadata dict to add to the collection

#### chroma_list_collections
List all collection names in the Chroma database with pagination support.

**Parameters:**
- `limit` (string, optional) - Optional maximum number of collections to return
- `offset` (string, optional) - Optional number of collections to skip before returning results

#### chroma_get_collection_info
Get information about a Chroma collection.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to get info about

#### chroma_get_collection_count
Get the number of documents in a Chroma collection.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to count

#### chroma_modify_collection
Modify a Chroma collection's name or metadata.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to modify
- `new_name` (string, optional) - Optional new name for the collection
- `new_metadata` (string, optional) - Optional new metadata for the collection

#### chroma_delete_collection
Delete a Chroma collection.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to delete

### Document Management

#### chroma_add_documents
Add documents to a Chroma collection.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to add documents to
- `documents` (array, required) - List of text documents to add
- `ids` (array, required) - List of IDs for the documents (required)
- `metadatas` (string, optional) - Optional list of metadata dictionaries for each document

#### chroma_get_documents
Get documents from a Chroma collection with optional filtering.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to get documents from
- `ids` (string, optional) - Optional list of document IDs to retrieve
- `where` (string, optional) - Optional metadata filters using Chroma's query operators
- `where_document` (string, optional) - Optional document content filters
- `include` (array, optional) - List of what to include in response. By default, this will include documents, and metadatas.
- `limit` (string, optional) - Optional maximum number of documents to return
- `offset` (string, optional) - Optional number of documents to skip before returning results

#### chroma_update_documents
Update documents in a Chroma collection.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to update documents in
- `ids` (array, required) - List of document IDs to update (required)
- `documents` (string, optional) - Optional list of new text documents.
- `embeddings` (string, optional) - Optional list of new embeddings for the documents.
- `metadatas` (string, optional) - Optional list of new metadata dictionaries for the documents.

#### chroma_delete_documents
Delete documents from a Chroma collection.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to delete documents from
- `ids` (array, required) - List of document IDs to delete

#### chroma_peek_collection
Peek at documents in a Chroma collection.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to peek into
- `limit` (integer, optional) - Number of documents to peek at

### Search and Query

#### chroma_query_documents
Query documents from a Chroma collection with advanced filtering.

**Parameters:**
- `collection_name` (string, required) - Name of the collection to query
- `query_texts` (array, required) - List of query texts to search for
- `n_results` (integer, optional) - Number of results to return per query
- `where` (string, optional) - Optional metadata filters using Chroma's query operators
- `where_document` (string, optional) - Optional document content filters
- `include` (array, optional) - List of what to include in response. By default, this will include documents, metadatas, and distances.

## Usage Examples

### Creating a Collection
```
chroma_create_collection:
  collection_name: "dating_app_development"
  embedding_function_name: "default"
  metadata: {"project": "likes_and_love", "type": "development_docs"}
```

### Adding Documents
```
chroma_add_documents:
  collection_name: "dating_app_development"
  documents: ["Created LoginScreen.tsx - authentication screen with form validation"]
  ids: ["login_screen_web"]
  metadatas: [{"type": "web_page", "category": "auth", "status": "completed"}]
```

### Querying Documents
```
chroma_query_documents:
  collection_name: "dating_app_development"
  query_texts: ["authentication screens"]
  n_results: 5
  where: {"type": "web_page", "category": "auth"}
```

### Getting Documents with Filters
```
chroma_get_documents:
  collection_name: "dating_app_development"
  where: {"status": "completed", "type": "mobile_screen"}
  limit: 10
```

## Metadata Structure for Dating App Project

### Standard Metadata Fields
- `type` - Type of component/screen (web_page, mobile_screen, component, service, etc.)
- `category` - Functional category (auth, profile, chat, events, places, etc.)
- `platform` - Platform (web, mobile, shared)
- `status` - Development status (planned, in_progress, completed, tested)
- `features` - List of key features implemented
- `framework` - Technology used (next_js, react_native, etc.)

### Example Metadata
```json
{
  "type": "web_page",
  "category": "authentication", 
  "platform": "web",
  "status": "completed",
  "features": "login,registration,validation,2fa",
  "framework": "next_js"
}
```

## Best Practices

1. **Consistent Naming**: Use descriptive, consistent IDs for documents
2. **Rich Metadata**: Include comprehensive metadata for better filtering
3. **Regular Updates**: Update document status as development progresses
4. **Semantic Descriptions**: Write clear, searchable document descriptions
5. **Categorization**: Use consistent category names across the project
6. **Version Control**: Consider including version information in metadata

## Project-Specific Collections

### dating_app_development
Main collection for storing all development progress, created components, screens, and features.

**Common Query Patterns:**
- Find all completed web pages: `where: {"type": "web_page", "status": "completed"}`
- Find mobile screens by category: `where: {"type": "mobile_screen", "category": "chat"}`
- Search for authentication features: `query_texts: ["authentication", "login", "registration"]`
