import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tabs,
  Tab,
  Badge,
  Paper,
  Divider,
} from '@mui/material';
import {
  Verified as VerifiedIcon,
  Cancel as CancelIcon,
  PhotoCamera as PhotoIcon,
  Description as DocumentIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Pending as PendingIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminService } from '../../../api/adminService';
import { VerificationRequest, VerificationStatus } from '../../../types/admin.types';
import styles from '../../../styles/admin/UserVerification.module.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const UserVerification: React.FC = () => {
  const queryClient = useQueryClient();
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedRequest, setSelectedRequest] = useState<VerificationRequest | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');

  // Загрузка запросов на верификацию
  const { data: verificationRequests, isLoading } = useQuery({
    queryKey: ['admin', 'verification-requests'],
    queryFn: () => adminService.getVerificationRequests(),
  });

  // Мутация для обработки верификации
  const processVerificationMutation = useMutation({
    mutationFn: ({ requestId, action, reason }: { 
      requestId: string; 
      action: 'approve' | 'reject'; 
      reason?: string 
    }) => adminService.processVerification(requestId, action, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'verification-requests'] });
      setDialogOpen(false);
      setSelectedRequest(null);
      setRejectReason('');
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleProcessVerification = (request: VerificationRequest, action: 'approve' | 'reject') => {
    setSelectedRequest(request);
    setActionType(action);
    setDialogOpen(true);
  };

  const handleConfirmAction = () => {
    if (!selectedRequest) return;

    processVerificationMutation.mutate({
      requestId: selectedRequest.id,
      action: actionType,
      reason: actionType === 'reject' ? rejectReason : undefined,
    });
  };

  const getStatusIcon = (status: VerificationStatus) => {
    switch (status) {
      case 'pending':
        return <PendingIcon color="warning" />;
      case 'approved':
        return <CheckIcon color="success" />;
      case 'rejected':
        return <ErrorIcon color="error" />;
      default:
        return <PendingIcon />;
    }
  };

  const getStatusColor = (status: VerificationStatus) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  const filterRequestsByStatus = (status: VerificationStatus) => {
    return verificationRequests?.filter(request => request.status === status) || [];
  };

  const renderVerificationCard = (request: VerificationRequest) => (
    <Card key={request.id} className={styles.verificationCard}>
      <CardContent>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3}>
            <Box className={styles.userInfo}>
              <Avatar src={request.user.avatar} className={styles.avatar}>
                {request.user.firstName[0]}{request.user.lastName[0]}
              </Avatar>
              <Box>
                <Typography variant="h6">
                  {request.user.firstName} {request.user.lastName}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {request.user.email}
                </Typography>
                <Chip
                  icon={getStatusIcon(request.status)}
                  label={request.status === 'pending' ? 'Ожидает' : 
                         request.status === 'approved' ? 'Одобрено' : 'Отклонено'}
                  color={getStatusColor(request.status) as any}
                  size="small"
                />
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Typography variant="subtitle2" gutterBottom>
              Документы для проверки:
            </Typography>
            <Box className={styles.documents}>
              {request.documents.map((doc, index) => (
                <Chip
                  key={index}
                  icon={doc.type === 'photo' ? <PhotoIcon /> : <DocumentIcon />}
                  label={doc.name}
                  variant="outlined"
                  size="small"
                  onClick={() => window.open(doc.url, '_blank')}
                  clickable
                />
              ))}
            </Box>
          </Grid>

          <Grid item xs={12} sm={3}>
            <Typography variant="body2" color="text.secondary">
              Подано: {new Date(request.submittedAt).toLocaleDateString('ru-RU')}
            </Typography>
            {request.processedAt && (
              <Typography variant="body2" color="text.secondary">
                Обработано: {new Date(request.processedAt).toLocaleDateString('ru-RU')}
              </Typography>
            )}
            {request.rejectionReason && (
              <Typography variant="body2" color="error">
                Причина отклонения: {request.rejectionReason}
              </Typography>
            )}
          </Grid>

          <Grid item xs={12} sm={2}>
            {request.status === 'pending' && (
              <Box className={styles.actions}>
                <Button
                  variant="contained"
                  color="success"
                  size="small"
                  startIcon={<CheckIcon />}
                  onClick={() => handleProcessVerification(request, 'approve')}
                  fullWidth
                  sx={{ mb: 1 }}
                >
                  Одобрить
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  size="small"
                  startIcon={<CancelIcon />}
                  onClick={() => handleProcessVerification(request, 'reject')}
                  fullWidth
                >
                  Отклонить
                </Button>
              </Box>
            )}
            <Button
              variant="outlined"
              size="small"
              startIcon={<ViewIcon />}
              onClick={() => {
                setSelectedRequest(request);
                // Открыть детальный просмотр
              }}
              fullWidth
              sx={{ mt: request.status === 'pending' ? 1 : 0 }}
            >
              Подробнее
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const pendingRequests = filterRequestsByStatus('pending');
  const approvedRequests = filterRequestsByStatus('approved');
  const rejectedRequests = filterRequestsByStatus('rejected');

  if (isLoading) {
    return <Typography>Загрузка...</Typography>;
  }

  return (
    <Box className={styles.container}>
      <Typography variant="h4" component="h1" gutterBottom>
        Верификация пользователей
      </Typography>

      <Paper className={styles.tabsContainer}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab 
            label={
              <Badge badgeContent={pendingRequests.length} color="warning">
                Ожидают проверки
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={approvedRequests.length} color="success">
                Одобренные
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={rejectedRequests.length} color="error">
                Отклоненные
              </Badge>
            } 
          />
        </Tabs>
      </Paper>

      <TabPanel value={currentTab} index={0}>
        <Box className={styles.requestsList}>
          {pendingRequests.length === 0 ? (
            <Alert severity="info">Нет запросов, ожидающих проверки</Alert>
          ) : (
            pendingRequests.map(renderVerificationCard)
          )}
        </Box>
      </TabPanel>

      <TabPanel value={currentTab} index={1}>
        <Box className={styles.requestsList}>
          {approvedRequests.length === 0 ? (
            <Alert severity="info">Нет одобренных запросов</Alert>
          ) : (
            approvedRequests.map(renderVerificationCard)
          )}
        </Box>
      </TabPanel>

      <TabPanel value={currentTab} index={2}>
        <Box className={styles.requestsList}>
          {rejectedRequests.length === 0 ? (
            <Alert severity="info">Нет отклоненных запросов</Alert>
          ) : (
            rejectedRequests.map(renderVerificationCard)
          )}
        </Box>
      </TabPanel>

      {/* Диалог подтверждения действия */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'approve' ? 'Одобрить верификацию' : 'Отклонить верификацию'}
        </DialogTitle>
        <DialogContent>
          {selectedRequest && (
            <Box>
              <Typography variant="body1" gutterBottom>
                Пользователь: {selectedRequest.user.firstName} {selectedRequest.user.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Email: {selectedRequest.user.email}
              </Typography>
              
              {actionType === 'reject' && (
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Причина отклонения"
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  required
                  sx={{ mt: 2 }}
                />
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Отмена
          </Button>
          <Button
            onClick={handleConfirmAction}
            variant="contained"
            color={actionType === 'approve' ? 'success' : 'error'}
            disabled={actionType === 'reject' && !rejectReason.trim()}
          >
            {actionType === 'approve' ? 'Одобрить' : 'Отклонить'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserVerification;
