import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Avatar,
  Chip,
  Divider,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
} from '@mui/material';
import {
  Person,
  Email,
  Phone,
  LocationOn,
  Cake,
  Work,
  School,
  Verified,
  Star,
  Block,
  Edit,
  Delete,
  Visibility,
  Message,
  Favorite,
  Report,
  Security,
  Payment,
  History,
  PhotoLibrary,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../api/adminApi';
import { User, UserActivity, UserReport } from '../../types/admin.types';
import { formatDate, formatDateTime, calculateAge } from '../../utils/dateUtils';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`user-tabpanel-${index}`}
      aria-labelledby={`user-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const UserShow: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const [tabValue, setTabValue] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [banDialogOpen, setBanDialogOpen] = useState(false);

  // Загрузка данных пользователя
  const {
    data: user,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['admin', 'user', id],
    queryFn: () => adminApi.getUser(id!),
    enabled: !!id,
  });

  // Загрузка активности пользователя
  const {
    data: userActivity,
  } = useQuery({
    queryKey: ['admin', 'user', id, 'activity'],
    queryFn: () => adminApi.getUserActivity(id!),
    enabled: !!id,
  });

  // Загрузка жалоб на пользователя
  const {
    data: userReports,
  } = useQuery({
    queryKey: ['admin', 'user', id, 'reports'],
    queryFn: () => adminApi.getUserReports(id!),
    enabled: !!id,
  });

  // Мутация для блокировки пользователя
  const banUserMutation = useMutation({
    mutationFn: (reason: string) => adminApi.banUser(id!, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'user', id] });
      setBanDialogOpen(false);
    },
  });

  // Мутация для удаления пользователя
  const deleteUserMutation = useMutation({
    mutationFn: () => adminApi.deleteUser(id!),
    onSuccess: () => {
      navigate('/users');
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleBanUser = () => {
    banUserMutation.mutate('Заблокирован администратором');
  };

  const handleDeleteUser = () => {
    deleteUserMutation.mutate();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'banned': return 'error';
      case 'suspended': return 'warning';
      case 'pending': return 'info';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Активен';
      case 'banned': return 'Заблокирован';
      case 'suspended': return 'Приостановлен';
      case 'pending': return 'Ожидает';
      default: return status;
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>Загрузка профиля пользователя...</Typography>
      </Box>
    );
  }

  if (error || !user) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Ошибка загрузки профиля пользователя
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Заголовок */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Профиль пользователя
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<Edit />}
            onClick={() => navigate(`/users/${id}/edit`)}
          >
            Редактировать
          </Button>
          
          <Button
            variant="outlined"
            color="warning"
            startIcon={<Block />}
            onClick={() => setBanDialogOpen(true)}
            disabled={user.status === 'banned'}
          >
            {user.status === 'banned' ? 'Заблокирован' : 'Заблокировать'}
          </Button>
          
          <Button
            variant="outlined"
            color="error"
            startIcon={<Delete />}
            onClick={() => setDeleteDialogOpen(true)}
          >
            Удалить
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Основная информация */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                src={user.avatar}
                sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
              >
                <Person sx={{ fontSize: 60 }} />
              </Avatar>
              
              <Typography variant="h5" gutterBottom>
                {user.firstName} {user.lastName}
                {user.isVerified && (
                  <Verified sx={{ ml: 1, color: 'primary.main', verticalAlign: 'middle' }} />
                )}
              </Typography>
              
              <Chip
                label={getStatusLabel(user.status)}
                color={getStatusColor(user.status) as any}
                sx={{ mb: 2 }}
              />
              
              {user.isPremium && (
                <Chip
                  label="Premium"
                  color="warning"
                  icon={<Star />}
                  sx={{ mb: 2, ml: 1 }}
                />
              )}

              <Typography variant="body2" color="text.secondary" gutterBottom>
                ID: {user.id}
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                Зарегистрирован: {formatDate(user.createdAt)}
              </Typography>
            </CardContent>
          </Card>

          {/* Быстрые действия */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Быстрые действия
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Message />}
                sx={{ mb: 1 }}
                onClick={() => navigate(`/messages?userId=${id}`)}
              >
                Сообщения
              </Button>
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<PhotoLibrary />}
                sx={{ mb: 1 }}
                onClick={() => navigate(`/users/${id}/photos`)}
              >
                Фотографии
              </Button>
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Report />}
                sx={{ mb: 1 }}
                onClick={() => navigate(`/users/${id}/reports`)}
              >
                Жалобы ({userReports?.length || 0})
              </Button>
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Payment />}
                onClick={() => navigate(`/users/${id}/payments`)}
              >
                Платежи
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Детальная информация */}
        <Grid item xs={12} md={8}>
          <Card>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab label="Основное" />
                <Tab label="Активность" />
                <Tab label="Статистика" />
                <Tab label="Безопасность" />
              </Tabs>
            </Box>

            {/* Основная информация */}
            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Email sx={{ mr: 1, color: 'action.active' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1">
                        {user.email}
                        {user.emailVerified && (
                          <Verified sx={{ ml: 1, fontSize: 16, color: 'success.main' }} />
                        )}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Phone sx={{ mr: 1, color: 'action.active' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Телефон
                      </Typography>
                      <Typography variant="body1">
                        {user.phone || 'Не указан'}
                        {user.phoneVerified && (
                          <Verified sx={{ ml: 1, fontSize: 16, color: 'success.main' }} />
                        )}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Cake sx={{ mr: 1, color: 'action.active' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Возраст
                      </Typography>
                      <Typography variant="body1">
                        {calculateAge(user.birthDate)} лет ({formatDate(user.birthDate)})
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocationOn sx={{ mr: 1, color: 'action.active' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Местоположение
                      </Typography>
                      <Typography variant="body1">
                        {user.location}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {user.occupation && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Work sx={{ mr: 1, color: 'action.active' }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Профессия
                        </Typography>
                        <Typography variant="body1">
                          {user.occupation}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {user.education && (
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <School sx={{ mr: 1, color: 'action.active' }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Образование
                        </Typography>
                        <Typography variant="body1">
                          {user.education}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {user.bio && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      О себе
                    </Typography>
                    <Typography variant="body1">
                      {user.bio}
                    </Typography>
                  </Grid>
                )}

                {user.interests && user.interests.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Интересы
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {user.interests.map((interest, index) => (
                        <Chip key={index} label={interest} size="small" />
                      ))}
                    </Box>
                  </Grid>
                )}
              </Grid>
            </TabPanel>

            {/* Активность */}
            <TabPanel value={tabValue} index={1}>
              {userActivity ? (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Действие</TableCell>
                        <TableCell>Дата</TableCell>
                        <TableCell>IP адрес</TableCell>
                        <TableCell>Устройство</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {userActivity.map((activity, index) => (
                        <TableRow key={index}>
                          <TableCell>{activity.action}</TableCell>
                          <TableCell>{formatDateTime(activity.timestamp)}</TableCell>
                          <TableCell>{activity.ipAddress}</TableCell>
                          <TableCell>{activity.device}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography>Нет данных об активности</Typography>
              )}
            </TabPanel>

            {/* Статистика */}
            <TabPanel value={tabValue} index={2}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Favorite sx={{ fontSize: 40, color: 'error.main', mb: 1 }} />
                      <Typography variant="h4">{user.stats?.likesGiven || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Лайков поставлено
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Favorite sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="h4">{user.stats?.likesReceived || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Лайков получено
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Message sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h4">{user.stats?.messagesSent || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Сообщений отправлено
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Visibility sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                      <Typography variant="h4">{user.stats?.profileViews || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Просмотров профиля
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Безопасность */}
            <TabPanel value={tabValue} index={3}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Alert severity={user.status === 'active' ? 'success' : 'warning'}>
                    Статус аккаунта: {getStatusLabel(user.status)}
                  </Alert>
                </Grid>

                {userReports && userReports.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Жалобы на пользователя ({userReports.length})
                    </Typography>
                    <TableContainer component={Paper}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Причина</TableCell>
                            <TableCell>От пользователя</TableCell>
                            <TableCell>Дата</TableCell>
                            <TableCell>Статус</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {userReports.map((report, index) => (
                            <TableRow key={index}>
                              <TableCell>{report.reason}</TableCell>
                              <TableCell>{report.reporterName}</TableCell>
                              <TableCell>{formatDateTime(report.createdAt)}</TableCell>
                              <TableCell>
                                <Chip
                                  label={report.status}
                                  size="small"
                                  color={report.status === 'resolved' ? 'success' : 'warning'}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>
                )}
              </Grid>
            </TabPanel>
          </Card>
        </Grid>
      </Grid>

      {/* Диалог блокировки */}
      <Dialog open={banDialogOpen} onClose={() => setBanDialogOpen(false)}>
        <DialogTitle>Заблокировать пользователя</DialogTitle>
        <DialogContent>
          <Typography>
            Вы уверены, что хотите заблокировать пользователя {user.firstName} {user.lastName}?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBanDialogOpen(false)}>Отмена</Button>
          <Button
            onClick={handleBanUser}
            color="warning"
            variant="contained"
            disabled={banUserMutation.isPending}
          >
            {banUserMutation.isPending ? 'Блокировка...' : 'Заблокировать'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог удаления */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Удалить пользователя</DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            Это действие нельзя отменить!
          </Alert>
          <Typography>
            Вы уверены, что хотите удалить пользователя {user.firstName} {user.lastName}?
            Все данные пользователя будут безвозвратно удалены.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Отмена</Button>
          <Button
            onClick={handleDeleteUser}
            color="error"
            variant="contained"
            disabled={deleteUserMutation.isPending}
          >
            {deleteUserMutation.isPending ? 'Удаление...' : 'Удалить'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserShow;
