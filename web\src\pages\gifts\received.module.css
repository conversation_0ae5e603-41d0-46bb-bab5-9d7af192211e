.container {
  padding: 2rem 0;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  background: linear-gradient(45deg, #e91e63, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.subtitle {
  margin-bottom: 2rem;
}

.filtersCard {
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.statsGrid {
  margin-bottom: 2rem;
}

.statCard {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.statContent {
  text-align: center;
  padding: 1.5rem;
}

.statIcon {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.statIcon svg {
  font-size: 2.5rem;
}

.statNumber {
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #e91e63, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.giftsGrid {
  margin-bottom: 2rem;
}

.giftCard {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.giftCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.unreadGift {
  border: 2px solid #f44336;
  box-shadow: 0 0 20px rgba(244, 67, 54, 0.3);
}

.unreadGift::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f44336, #e91e63);
  z-index: 1;
}

.giftHeader {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.giftImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.giftCard:hover .giftImage {
  transform: scale(1.05);
}

.statusChip {
  position: absolute;
  top: 8px;
  right: 8px;
  backdrop-filter: blur(4px);
  background: rgba(255, 255, 255, 0.9);
}

.newBadge {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}

.giftContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.giftName {
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.senderInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.giftDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.giftPrice {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 600;
  color: #ff9800;
}

.giftMessage {
  font-style: italic;
  color: #666;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: linear-gradient(45deg, #f8f9fa, #fff);
  border-radius: 8px;
  border-left: 4px solid #e91e63;
  position: relative;
}

.giftMessage::before {
  content: '"';
  position: absolute;
  top: 0.25rem;
  left: 0.5rem;
  font-size: 1.5rem;
  color: #e91e63;
  font-weight: bold;
}

.giftMessage::after {
  content: '"';
  position: absolute;
  bottom: 0.25rem;
  right: 0.5rem;
  font-size: 1.5rem;
  color: #e91e63;
  font-weight: bold;
}

.giftActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: auto;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.errorAlert,
.emptyAlert {
  margin: 2rem 0;
  border-radius: 12px;
  text-align: center;
}

.emptyAlert {
  padding: 2rem;
  background: linear-gradient(45deg, #f8f9fa, #fff);
}

.thankYouContent {
  padding: 1rem 0;
}

.selectedGift {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(45deg, #f8f9fa, #fff);
  border-radius: 12px;
  border: 2px solid #e3f2fd;
}

.selectedGiftImage {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
}

/* Pulse animation for unread gifts */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

.unreadGift {
  animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0;
  }
  
  .header {
    margin-bottom: 1.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .filtersCard,
  .statsGrid {
    margin-bottom: 1.5rem;
  }
  
  .statContent {
    padding: 1rem;
  }
  
  .statIcon svg {
    font-size: 2rem;
  }
  
  .statNumber {
    font-size: 1.75rem;
  }
  
  .giftDetails {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .senderInfo {
    flex-direction: column;
    text-align: center;
  }
  
  .giftMessage {
    padding: 0.5rem;
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.75rem;
  }
  
  .giftCard {
    margin-bottom: 1rem;
  }
  
  .giftContent {
    padding: 0.75rem;
  }
  
  .paginationContainer {
    margin-top: 2rem;
  }
  
  .emptyAlert {
    padding: 1.5rem;
  }
  
  .selectedGift {
    flex-direction: column;
    text-align: center;
  }
}

/* Status-specific styles */
.giftCard[data-status="unread"] {
  border-left: 4px solid #f44336;
}

.giftCard[data-status="viewed"] {
  border-left: 4px solid #2196f3;
}

.giftCard[data-status="thanked"] {
  border-left: 4px solid #4caf50;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .giftCard,
  .giftImage,
  .statCard {
    transition: none;
  }
  
  .giftCard:hover,
  .statCard:hover {
    transform: none;
  }
  
  .giftCard:hover .giftImage {
    transform: none;
  }
  
  .unreadGift {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .giftCard {
    border: 2px solid #000;
  }
  
  .statusChip {
    border: 1px solid #000;
  }
  
  .statCard {
    border: 2px solid #000;
  }
  
  .unreadGift {
    border-color: #000;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .giftMessage {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
    color: #ccc;
    border-left-color: #bb86fc;
  }
  
  .giftMessage::before,
  .giftMessage::after {
    color: #bb86fc;
  }
  
  .emptyAlert {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
  }
  
  .selectedGift {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
    border-color: #555;
  }
  
  .statusChip {
    background: rgba(45, 45, 45, 0.9);
  }
}

/* Special effects for gift interactions */
.giftCard.viewed {
  position: relative;
  overflow: hidden;
}

.giftCard.viewed::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3rem;
  color: #4caf50;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  animation: checkmark 0.5s ease-in-out;
}

@keyframes checkmark {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Thank you animation */
.giftCard.thanked {
  position: relative;
}

.giftCard.thanked::before {
  content: '💖';
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.5rem;
  animation: heartbeat 1s ease-in-out infinite;
  z-index: 3;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
