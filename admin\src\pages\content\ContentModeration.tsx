import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Avatar,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  TextField,
  MenuItem,
  Pagination,
} from '@mui/material';
import {
  Visibility,
  Delete,
  Block,
  CheckCircle,
  Warning,
  Report,
  Image as ImageIcon,
  VideoLibrary,
  Message,
  Person,
  FilterList,
  Search,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../api/adminApi';
import { ContentItem, ContentReport } from '../../types/admin.types';
import { formatDateTime } from '../../utils/dateUtils';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`content-tabpanel-${index}`}
      aria-labelledby={`content-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ContentModeration: React.FC = () => {
  const queryClient = useQueryClient();
  
  const [tabValue, setTabValue] = useState(0);
  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [moderationDialogOpen, setModerationDialogOpen] = useState(false);
  
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    search: '',
  });
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);

  // Загрузка контента для модерации
  const {
    data: contentData,
    isLoading: contentLoading,
  } = useQuery({
    queryKey: ['admin', 'content', 'moderation', filters, page],
    queryFn: () => adminApi.getContentForModeration({ ...filters, page, pageSize }),
  });

  // Загрузка жалоб на контент
  const {
    data: reportsData,
    isLoading: reportsLoading,
  } = useQuery({
    queryKey: ['admin', 'content', 'reports', page],
    queryFn: () => adminApi.getContentReports({ page, pageSize }),
  });

  // Мутация для модерации контента
  const moderateContentMutation = useMutation({
    mutationFn: ({ id, action, reason }: { id: string; action: 'approve' | 'reject' | 'delete'; reason?: string }) =>
      adminApi.moderateContent(id, action, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'content'] });
      setModerationDialogOpen(false);
      setSelectedContent(null);
    },
  });

  // Мутация для обработки жалоб
  const handleReportMutation = useMutation({
    mutationFn: ({ reportId, action }: { reportId: string; action: 'resolve' | 'dismiss' }) =>
      adminApi.handleContentReport(reportId, action),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'content', 'reports'] });
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleViewContent = (content: ContentItem) => {
    setSelectedContent(content);
    setViewDialogOpen(true);
  };

  const handleModerateContent = (content: ContentItem) => {
    setSelectedContent(content);
    setModerationDialogOpen(true);
  };

  const handleDeleteContent = (content: ContentItem) => {
    setSelectedContent(content);
    setDeleteDialogOpen(true);
  };

  const handleApproveContent = (content: ContentItem) => {
    moderateContentMutation.mutate({ id: content.id, action: 'approve' });
  };

  const handleRejectContent = (content: ContentItem, reason: string) => {
    moderateContentMutation.mutate({ id: content.id, action: 'reject', reason });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'success';
      case 'rejected': return 'error';
      case 'pending': return 'warning';
      case 'reported': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'approved': return 'Одобрено';
      case 'rejected': return 'Отклонено';
      case 'pending': return 'На модерации';
      case 'reported': return 'Жалобы';
      default: return status;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'photo': return <ImageIcon />;
      case 'video': return <VideoLibrary />;
      case 'message': return <Message />;
      default: return <ImageIcon />;
    }
  };

  const renderContentGrid = (content: ContentItem[]) => (
    <ImageList cols={4} gap={8}>
      {content.map((item) => (
        <ImageListItem key={item.id}>
          {item.type === 'photo' ? (
            <img
              src={item.url}
              alt={item.description || 'Content'}
              loading="lazy"
              style={{ height: 200, objectFit: 'cover' }}
            />
          ) : item.type === 'video' ? (
            <video
              src={item.url}
              style={{ height: 200, objectFit: 'cover' }}
              controls={false}
            />
          ) : (
            <Box
              sx={{
                height: 200,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'grey.100',
              }}
            >
              <Message sx={{ fontSize: 48, color: 'grey.400' }} />
            </Box>
          )}
          
          <ImageListItemBar
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getTypeIcon(item.type)}
                <Chip
                  label={getStatusLabel(item.status)}
                  size="small"
                  color={getStatusColor(item.status) as any}
                />
              </Box>
            }
            subtitle={`${item.user.firstName} ${item.user.lastName}`}
            actionIcon={
              <Box>
                <IconButton
                  sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                  onClick={() => handleViewContent(item)}
                >
                  <Visibility />
                </IconButton>
                
                {item.status === 'pending' && (
                  <>
                    <IconButton
                      sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                      onClick={() => handleApproveContent(item)}
                    >
                      <CheckCircle />
                    </IconButton>
                    
                    <IconButton
                      sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                      onClick={() => handleModerateContent(item)}
                    >
                      <Block />
                    </IconButton>
                  </>
                )}
                
                <IconButton
                  sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                  onClick={() => handleDeleteContent(item)}
                >
                  <Delete />
                </IconButton>
              </Box>
            }
          />
        </ImageListItem>
      ))}
    </ImageList>
  );

  const renderReportsTable = (reports: ContentReport[]) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Контент</TableCell>
            <TableCell>Пользователь</TableCell>
            <TableCell>Жалоба от</TableCell>
            <TableCell>Причина</TableCell>
            <TableCell>Дата</TableCell>
            <TableCell>Статус</TableCell>
            <TableCell>Действия</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {reports.map((report) => (
            <TableRow key={report.id}>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getTypeIcon(report.content.type)}
                  <Typography variant="body2">
                    {report.content.type}
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Avatar src={report.content.user.avatar} sx={{ width: 32, height: 32 }}>
                    <Person />
                  </Avatar>
                  <Typography variant="body2">
                    {report.content.user.firstName} {report.content.user.lastName}
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2">
                  {report.reporter.firstName} {report.reporter.lastName}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2">
                  {report.reason}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2">
                  {formatDateTime(report.createdAt)}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Chip
                  label={report.status}
                  size="small"
                  color={report.status === 'resolved' ? 'success' : 'warning'}
                />
              </TableCell>
              
              <TableCell>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => handleViewContent(report.content)}
                  >
                    Просмотр
                  </Button>
                  
                  {report.status === 'pending' && (
                    <>
                      <Button
                        size="small"
                        variant="contained"
                        color="success"
                        onClick={() => handleReportMutation.mutate({ reportId: report.id, action: 'resolve' })}
                      >
                        Решить
                      </Button>
                      
                      <Button
                        size="small"
                        variant="outlined"
                        color="error"
                        onClick={() => handleReportMutation.mutate({ reportId: report.id, action: 'dismiss' })}
                      >
                        Отклонить
                      </Button>
                    </>
                  )}
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Модерация контента
      </Typography>

      {/* Фильтры */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Статус"
                value={filters.status}
                onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                fullWidth
                size="small"
              >
                <MenuItem value="all">Все</MenuItem>
                <MenuItem value="pending">На модерации</MenuItem>
                <MenuItem value="approved">Одобрено</MenuItem>
                <MenuItem value="rejected">Отклонено</MenuItem>
                <MenuItem value="reported">С жалобами</MenuItem>
              </TextField>
            </Grid>
            
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Тип контента"
                value={filters.type}
                onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                fullWidth
                size="small"
              >
                <MenuItem value="all">Все</MenuItem>
                <MenuItem value="photo">Фото</MenuItem>
                <MenuItem value="video">Видео</MenuItem>
                <MenuItem value="message">Сообщения</MenuItem>
              </TextField>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                label="Поиск"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                fullWidth
                size="small"
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'action.active' }} />,
                }}
                placeholder="Поиск по пользователю или описанию..."
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Вкладки */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Контент на модерации" />
            <Tab label="Жалобы на контент" />
          </Tabs>
        </Box>

        {/* Контент на модерации */}
        <TabPanel value={tabValue} index={0}>
          {contentLoading ? (
            <Typography>Загрузка...</Typography>
          ) : contentData?.items?.length ? (
            <>
              {renderContentGrid(contentData.items)}
              
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Pagination
                  count={Math.ceil(contentData.total / pageSize)}
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                />
              </Box>
            </>
          ) : (
            <Alert severity="info">
              Нет контента для модерации
            </Alert>
          )}
        </TabPanel>

        {/* Жалобы на контент */}
        <TabPanel value={tabValue} index={1}>
          {reportsLoading ? (
            <Typography>Загрузка...</Typography>
          ) : reportsData?.items?.length ? (
            <>
              {renderReportsTable(reportsData.items)}
              
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Pagination
                  count={Math.ceil(reportsData.total / pageSize)}
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                />
              </Box>
            </>
          ) : (
            <Alert severity="info">
              Нет жалоб на контент
            </Alert>
          )}
        </TabPanel>
      </Card>

      {/* Диалог просмотра контента */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Просмотр контента</DialogTitle>
        <DialogContent>
          {selectedContent && (
            <Box>
              {selectedContent.type === 'photo' && (
                <img
                  src={selectedContent.url}
                  alt="Content"
                  style={{ width: '100%', maxHeight: 400, objectFit: 'contain' }}
                />
              )}
              
              {selectedContent.type === 'video' && (
                <video
                  src={selectedContent.url}
                  controls
                  style={{ width: '100%', maxHeight: 400 }}
                />
              )}
              
              {selectedContent.description && (
                <Typography variant="body1" sx={{ mt: 2 }}>
                  {selectedContent.description}
                </Typography>
              )}
              
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Пользователь: {selectedContent.user.firstName} {selectedContent.user.lastName}
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                Дата: {formatDateTime(selectedContent.createdAt)}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Закрыть</Button>
        </DialogActions>
      </Dialog>

      {/* Диалог модерации */}
      <Dialog
        open={moderationDialogOpen}
        onClose={() => setModerationDialogOpen(false)}
      >
        <DialogTitle>Модерация контента</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Выберите действие для данного контента:
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setModerationDialogOpen(false)}>Отмена</Button>
          <Button
            onClick={() => selectedContent && handleApproveContent(selectedContent)}
            color="success"
            variant="contained"
          >
            Одобрить
          </Button>
          <Button
            onClick={() => selectedContent && handleRejectContent(selectedContent, 'Нарушение правил')}
            color="error"
            variant="contained"
          >
            Отклонить
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог удаления */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Удалить контент</DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            Это действие нельзя отменить!
          </Alert>
          <Typography>
            Вы уверены, что хотите удалить этот контент?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Отмена</Button>
          <Button
            onClick={() => {
              if (selectedContent) {
                moderateContentMutation.mutate({ id: selectedContent.id, action: 'delete' });
                setDeleteDialogOpen(false);
              }
            }}
            color="error"
            variant="contained"
          >
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContentModeration;
