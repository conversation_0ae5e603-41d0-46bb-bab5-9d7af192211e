import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  StyleSheet,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { subscriptionService } from '../../services/subscriptionService';
import { SubscriptionPlan, SubscriptionStatus } from '../../types/subscription.types';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { FeatureCard } from '../../components/FeatureCard';
import { PlanCard } from '../../components/PlanCard';
import { useTheme } from '../../hooks/useTheme';
import { useAuth } from '../../hooks/useAuth';
import { colors, spacing, typography } from '../../styles/theme';

interface ManageSubscriptionScreenProps {}

const ManageSubscriptionScreen: React.FC<ManageSubscriptionScreenProps> = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [autoRenewal, setAutoRenewal] = useState(true);

  // Загрузка текущей подписки
  const { data: currentSubscription, isLoading: subscriptionLoading } = useQuery({
    queryKey: ['subscription', 'current'],
    queryFn: () => subscriptionService.getCurrentSubscription(),
  });

  // Загрузка доступных планов
  const { data: availablePlans, isLoading: plansLoading } = useQuery({
    queryKey: ['subscription', 'plans'],
    queryFn: () => subscriptionService.getAvailablePlans(),
  });

  // Загрузка истории платежей
  const { data: paymentHistory } = useQuery({
    queryKey: ['subscription', 'payments'],
    queryFn: () => subscriptionService.getPaymentHistory(),
  });

  // Мутация для отмены подписки
  const cancelSubscriptionMutation = useMutation({
    mutationFn: () => subscriptionService.cancelSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      Alert.alert('Успешно', 'Подписка отменена');
    },
    onError: () => {
      Alert.alert('Ошибка', 'Не удалось отменить подписку');
    },
  });

  // Мутация для возобновления подписки
  const resumeSubscriptionMutation = useMutation({
    mutationFn: () => subscriptionService.resumeSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      Alert.alert('Успешно', 'Подписка возобновлена');
    },
    onError: () => {
      Alert.alert('Ошибка', 'Не удалось возобновить подписку');
    },
  });

  // Мутация для изменения автопродления
  const updateAutoRenewalMutation = useMutation({
    mutationFn: (enabled: boolean) => subscriptionService.updateAutoRenewal(enabled),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
    onError: () => {
      Alert.alert('Ошибка', 'Не удалось изменить настройки автопродления');
      setAutoRenewal(!autoRenewal); // Возвращаем предыдущее состояние
    },
  });

  const handleCancelSubscription = useCallback(() => {
    Alert.alert(
      'Отменить подписку',
      'Вы уверены, что хотите отменить подписку? Вы потеряете доступ к премиум-функциям.',
      [
        { text: 'Отмена', style: 'cancel' },
        { 
          text: 'Отменить подписку', 
          style: 'destructive',
          onPress: () => cancelSubscriptionMutation.mutate()
        },
      ]
    );
  }, [cancelSubscriptionMutation]);

  const handleResumeSubscription = useCallback(() => {
    resumeSubscriptionMutation.mutate();
  }, [resumeSubscriptionMutation]);

  const handleUpgradePlan = useCallback((planId: string) => {
    navigation.navigate('Payment', { planId, isUpgrade: true });
  }, [navigation]);

  const handleAutoRenewalToggle = useCallback((value: boolean) => {
    setAutoRenewal(value);
    updateAutoRenewalMutation.mutate(value);
  }, [updateAutoRenewalMutation]);

  const getStatusColor = (status: SubscriptionStatus) => {
    switch (status) {
      case 'active':
        return colors.success;
      case 'cancelled':
        return colors.warning;
      case 'expired':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusText = (status: SubscriptionStatus) => {
    switch (status) {
      case 'active':
        return 'Активна';
      case 'cancelled':
        return 'Отменена';
      case 'expired':
        return 'Истекла';
      default:
        return 'Неизвестно';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
    }).format(price);
  };

  if (subscriptionLoading || plansLoading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Управление подпиской</Text>
        </View>

        {/* Текущая подписка */}
        {currentSubscription && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Текущая подписка</Text>
            <View style={styles.subscriptionCard}>
              <LinearGradient
                colors={[colors.primary, colors.secondary]}
                style={styles.subscriptionGradient}
              >
                <View style={styles.subscriptionHeader}>
                  <View>
                    <Text style={styles.planName}>{currentSubscription.planName}</Text>
                    <Text style={styles.planPrice}>
                      {formatPrice(currentSubscription.price)}/месяц
                    </Text>
                  </View>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(currentSubscription.status) }]}>
                    <Text style={styles.statusText}>
                      {getStatusText(currentSubscription.status)}
                    </Text>
                  </View>
                </View>

                <View style={styles.subscriptionDetails}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Начало:</Text>
                    <Text style={styles.detailValue}>
                      {formatDate(currentSubscription.startDate)}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Окончание:</Text>
                    <Text style={styles.detailValue}>
                      {formatDate(currentSubscription.endDate)}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Автопродление:</Text>
                    <Switch
                      value={autoRenewal}
                      onValueChange={handleAutoRenewalToggle}
                      trackColor={{ false: colors.surface, true: colors.accent }}
                      thumbColor={autoRenewal ? colors.white : colors.textSecondary}
                    />
                  </View>
                </View>
              </LinearGradient>
            </View>

            {/* Действия с подпиской */}
            <View style={styles.actionsContainer}>
              {currentSubscription.status === 'active' && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.cancelButton]}
                  onPress={handleCancelSubscription}
                  disabled={cancelSubscriptionMutation.isPending}
                >
                  {cancelSubscriptionMutation.isPending ? (
                    <ActivityIndicator color={colors.white} />
                  ) : (
                    <>
                      <Ionicons name="close-circle-outline" size={20} color={colors.white} />
                      <Text style={styles.actionButtonText}>Отменить подписку</Text>
                    </>
                  )}
                </TouchableOpacity>
              )}

              {currentSubscription.status === 'cancelled' && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.resumeButton]}
                  onPress={handleResumeSubscription}
                  disabled={resumeSubscriptionMutation.isPending}
                >
                  {resumeSubscriptionMutation.isPending ? (
                    <ActivityIndicator color={colors.white} />
                  ) : (
                    <>
                      <Ionicons name="play-circle-outline" size={20} color={colors.white} />
                      <Text style={styles.actionButtonText}>Возобновить подписку</Text>
                    </>
                  )}
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}

        {/* Доступные планы для апгрейда */}
        {availablePlans && availablePlans.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Доступные планы</Text>
            {availablePlans.map((plan) => (
              <PlanCard
                key={plan.id}
                plan={plan}
                isCurrentPlan={currentSubscription?.planId === plan.id}
                onSelect={() => handleUpgradePlan(plan.id)}
                showUpgradeButton={currentSubscription?.planId !== plan.id}
              />
            ))}
          </View>
        )}

        {/* История платежей */}
        {paymentHistory && paymentHistory.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>История платежей</Text>
            {paymentHistory.slice(0, 5).map((payment) => (
              <View key={payment.id} style={styles.paymentItem}>
                <View style={styles.paymentInfo}>
                  <Text style={styles.paymentDate}>
                    {formatDate(payment.date)}
                  </Text>
                  <Text style={styles.paymentDescription}>
                    {payment.description}
                  </Text>
                </View>
                <Text style={styles.paymentAmount}>
                  {formatPrice(payment.amount)}
                </Text>
              </View>
            ))}
            
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={() => navigation.navigate('PaymentHistory')}
            >
              <Text style={styles.viewAllText}>Показать всю историю</Text>
              <Ionicons name="chevron-forward" size={16} color={colors.primary} />
            </TouchableOpacity>
          </View>
        )}

        {/* Преимущества подписки */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Преимущества подписки</Text>
          <FeatureCard
            icon="heart"
            title="Безлимитные лайки"
            description="Ставьте лайки без ограничений"
            isActive={currentSubscription?.status === 'active'}
          />
          <FeatureCard
            icon="eye"
            title="Кто вас лайкнул"
            description="Смотрите, кто поставил вам лайк"
            isActive={currentSubscription?.status === 'active'}
          />
          <FeatureCard
            icon="flash"
            title="Суперлайки"
            description="5 суперлайков в день"
            isActive={currentSubscription?.status === 'active'}
          />
          <FeatureCard
            icon="location"
            title="Изменение местоположения"
            description="Знакомьтесь в любом городе"
            isActive={currentSubscription?.status === 'active'}
          />
        </View>

        {/* Поддержка */}
        <View style={styles.section}>
          <TouchableOpacity
            style={styles.supportButton}
            onPress={() => navigation.navigate('Support')}
          >
            <Ionicons name="help-circle-outline" size={24} color={colors.primary} />
            <Text style={styles.supportText}>Нужна помощь?</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  backButton: {
    marginRight: spacing.md,
  },
  title: {
    ...typography.h1,
    color: colors.text,
  },
  section: {
    paddingHorizontal: spacing.md,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.text,
    marginBottom: spacing.md,
  },
  subscriptionCard: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: spacing.md,
  },
  subscriptionGradient: {
    padding: spacing.lg,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  planName: {
    ...typography.h2,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  planPrice: {
    ...typography.body,
    color: colors.white,
    opacity: 0.8,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },
  subscriptionDetails: {
    gap: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    ...typography.body,
    color: colors.white,
    opacity: 0.8,
  },
  detailValue: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
  actionsContainer: {
    gap: spacing.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.sm,
  },
  cancelButton: {
    backgroundColor: colors.error,
  },
  resumeButton: {
    backgroundColor: colors.success,
  },
  actionButtonText: {
    ...typography.button,
    color: colors.white,
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  paymentInfo: {
    flex: 1,
  },
  paymentDate: {
    ...typography.caption,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  paymentDescription: {
    ...typography.body,
    color: colors.text,
  },
  paymentAmount: {
    ...typography.h4,
    color: colors.text,
    fontWeight: '600',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    gap: spacing.xs,
  },
  viewAllText: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.surface,
    borderRadius: 12,
    gap: spacing.md,
  },
  supportText: {
    ...typography.body,
    color: colors.text,
    flex: 1,
  },
});

export default ManageSubscriptionScreen;
