import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  Alert,
  FormControlLabel,
  Switch,
  TextField,
  MenuItem,
  Button,
  Avatar,
  IconButton,
  Snackbar,
  LinearProgress,
} from '@mui/material';
import {
  PhotoCamera,
  Visibility,
  VisibilityOff,
  Person,
  Email,
  Phone,
  LocationOn,
  Cake,
  Work,
  School,
  Save,
  Cancel,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../api/adminApi';
import { User } from '../../types/admin.types';

// Validation schema
const userEditSchema = yup.object().shape({
  email: yup.string().email('Неверный формат email').required('Email обязателен'),
  firstName: yup.string().required('Имя обязательно').min(2, 'Минимум 2 символа'),
  lastName: yup.string().required('Фамилия обязательна').min(2, 'Минимум 2 символа'),
  phone: yup.string().matches(/^\+?[1-9]\d{1,14}$/, 'Неверный формат телефона'),
  birthDate: yup.date().required('Дата рождения обязательна').max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Минимальный возраст 18 лет'),
  gender: yup.string().oneOf(['male', 'female', 'other'], 'Выберите пол').required('Пол обязателен'),
  location: yup.string().required('Местоположение обязательно'),
});

interface UserEditData {
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  birthDate: Date;
  gender: 'male' | 'female' | 'other';
  location: string;
  bio?: string;
  occupation?: string;
  education?: string;
  interests?: string[];
  isVerified?: boolean;
  isPremium?: boolean;
  isActive?: boolean;
  role?: 'user' | 'moderator' | 'admin';
}

const UserEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const [notification, setNotification] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Загрузка данных пользователя
  const {
    data: user,
    isLoading: userLoading,
    error,
  } = useQuery({
    queryKey: ['admin', 'user', id],
    queryFn: () => adminApi.getUser(id!),
    enabled: !!id,
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    setValue,
  } = useForm<UserEditData>({
    resolver: yupResolver(userEditSchema),
  });

  // Заполнение формы данными пользователя
  useEffect(() => {
    if (user) {
      reset({
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone || '',
        birthDate: new Date(user.birthDate),
        gender: user.gender,
        location: user.location,
        bio: user.bio || '',
        occupation: user.occupation || '',
        education: user.education || '',
        interests: user.interests || [],
        isVerified: user.isVerified,
        isPremium: user.isPremium,
        isActive: user.status === 'active',
        role: user.role,
      });
      setAvatarPreview(user.avatar);
    }
  }, [user, reset]);

  // Мутация для обновления пользователя
  const updateUserMutation = useMutation({
    mutationFn: (data: UserEditData) => adminApi.updateUser(id!, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'user', id] });
      setNotification({ message: 'Пользователь успешно обновлен', type: 'success' });
    },
    onError: (error: any) => {
      setNotification({ message: error.message || 'Ошибка при обновлении пользователя', type: 'error' });
    },
  });

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: UserEditData) => {
    updateUserMutation.mutate(data);
  };

  const genderOptions = [
    { value: 'male', label: 'Мужской' },
    { value: 'female', label: 'Женский' },
    { value: 'other', label: 'Другой' },
  ];

  const roleOptions = [
    { value: 'user', label: 'Пользователь' },
    { value: 'moderator', label: 'Модератор' },
    { value: 'admin', label: 'Администратор' },
  ];

  if (userLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>Загрузка данных пользователя...</Typography>
      </Box>
    );
  }

  if (error || !user) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Ошибка загрузки данных пользователя
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Редактирование пользователя
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        Редактируйте информацию о пользователе {user.firstName} {user.lastName}
      </Alert>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={3}>
          {/* Основная информация */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Основная информация
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="firstName"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Имя"
                          fullWidth
                          error={!!errors.firstName}
                          helperText={errors.firstName?.message}
                          required
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="lastName"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Фамилия"
                          fullWidth
                          error={!!errors.lastName}
                          helperText={errors.lastName?.message}
                          required
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="email"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Email"
                          type="email"
                          fullWidth
                          error={!!errors.email}
                          helperText={errors.email?.message}
                          required
                          InputProps={{
                            startAdornment: <Email sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="phone"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Телефон"
                          fullWidth
                          error={!!errors.phone}
                          helperText={errors.phone?.message}
                          InputProps={{
                            startAdornment: <Phone sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="birthDate"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          value={field.value ? field.value.toISOString().split('T')[0] : ''}
                          onChange={(e) => field.onChange(new Date(e.target.value))}
                          label="Дата рождения"
                          type="date"
                          fullWidth
                          error={!!errors.birthDate}
                          helperText={errors.birthDate?.message}
                          required
                          InputLabelProps={{ shrink: true }}
                          InputProps={{
                            startAdornment: <Cake sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="gender"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          select
                          label="Пол"
                          fullWidth
                          error={!!errors.gender}
                          helperText={errors.gender?.message}
                          required
                        >
                          {genderOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </TextField>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="location"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Местоположение"
                          fullWidth
                          error={!!errors.location}
                          helperText={errors.location?.message}
                          required
                          InputProps={{
                            startAdornment: <LocationOn sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="bio"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="О себе"
                          multiline
                          rows={3}
                          fullWidth
                          placeholder="Расскажите о пользователе..."
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Фото профиля и настройки */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Фото профиля
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    src={avatarPreview || undefined}
                    sx={{ width: 120, height: 120, mb: 2 }}
                  >
                    <Person sx={{ fontSize: 60 }} />
                  </Avatar>

                  <input
                    accept="image/*"
                    style={{ display: 'none' }}
                    id="avatar-upload"
                    type="file"
                    onChange={handleAvatarChange}
                  />
                  <label htmlFor="avatar-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<PhotoCamera />}
                    >
                      Изменить фото
                    </Button>
                  </label>
                </Box>

                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Настройки аккаунта
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Controller
                  name="role"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      label="Роль"
                      fullWidth
                      sx={{ mb: 2 }}
                    >
                      {roleOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </TextField>
                  )}
                />

                <Controller
                  name="isVerified"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label="Верифицированный"
                      sx={{ mb: 1, display: 'block' }}
                    />
                  )}
                />

                <Controller
                  name="isPremium"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label="Premium аккаунт"
                      sx={{ mb: 1, display: 'block' }}
                    />
                  )}
                />

                <Controller
                  name="isActive"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label="Активный"
                      sx={{ mb: 1, display: 'block' }}
                    />
                  )}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Дополнительная информация */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Дополнительная информация
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="occupation"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Профессия"
                          fullWidth
                          InputProps={{
                            startAdornment: <Work sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="education"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Образование"
                          fullWidth
                          InputProps={{
                            startAdornment: <School sx={{ mr: 1, color: 'action.active' }} />,
                          }}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Кнопки действий */}
        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={!isDirty || updateUserMutation.isPending}
            startIcon={<Save />}
          >
            {updateUserMutation.isPending ? 'Сохранение...' : 'Сохранить изменения'}
          </Button>

          <Button
            variant="outlined"
            size="large"
            onClick={() => navigate(`/users/${id}`)}
            startIcon={<Cancel />}
          >
            Отмена
          </Button>
        </Box>
      </form>

      {/* Уведомления */}
      <Snackbar
        open={!!notification}
        autoHideDuration={6000}
        onClose={() => setNotification(null)}
        message={notification?.message}
      />
    </Box>
  );
};

export default UserEdit;
