import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Avatar,
  Alert,
  Pagination,
} from '@mui/material';
import {
  Star as StarIcon,
  Diamond as DiamondIcon,
  Person as PersonIcon,
  Edit as EditIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  GetApp as ExportIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Line, Doughnut } from 'react-chartjs-2';
import { adminService } from '../../../api/adminService';
import { SubscriptionData, SubscriptionType, SubscriptionStatus } from '../../../types/admin.types';
import styles from '../../../styles/admin/Subscriptions.module.css';

const Subscriptions: React.FC = () => {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<SubscriptionStatus | 'all'>('all');
  const [typeFilter, setTypeFilter] = useState<SubscriptionType | 'all'>('all');
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionData | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'extend' | 'cancel' | 'upgrade'>('extend');
  const [extensionDays, setExtensionDays] = useState('30');
  const [reason, setReason] = useState('');

  // Загрузка данных о подписках
  const { data: subscriptionsData, isLoading, refetch } = useQuery({
    queryKey: ['admin', 'subscriptions', page, statusFilter, typeFilter],
    queryFn: () => adminService.getSubscriptions({
      page,
      limit: 20,
      status: statusFilter === 'all' ? undefined : statusFilter,
      type: typeFilter === 'all' ? undefined : typeFilter,
    }),
  });

  // Загрузка статистики подписок
  const { data: subscriptionStats } = useQuery({
    queryKey: ['admin', 'subscription-stats'],
    queryFn: () => adminService.getSubscriptionStats(),
  });

  // Мутация для управления подписками
  const manageSubscriptionMutation = useMutation({
    mutationFn: ({ subscriptionId, action, data }: {
      subscriptionId: string;
      action: 'extend' | 'cancel' | 'upgrade';
      data?: any;
    }) => adminService.manageSubscription(subscriptionId, action, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscription-stats'] });
      setDialogOpen(false);
      setSelectedSubscription(null);
      setReason('');
    },
  });

  const handleSubscriptionAction = (subscription: SubscriptionData, action: 'extend' | 'cancel' | 'upgrade') => {
    setSelectedSubscription(subscription);
    setActionType(action);
    setDialogOpen(true);
  };

  const handleConfirmAction = () => {
    if (!selectedSubscription) return;

    const actionData: any = { reason };
    
    if (actionType === 'extend') {
      actionData.days = parseInt(extensionDays);
    }

    manageSubscriptionMutation.mutate({
      subscriptionId: selectedSubscription.id,
      action: actionType,
      data: actionData,
    });
  };

  const getSubscriptionTypeIcon = (type: SubscriptionType) => {
    switch (type) {
      case 'premium':
        return <StarIcon color="warning" />;
      case 'vip':
        return <DiamondIcon color="error" />;
      default:
        return <PersonIcon />;
    }
  };

  const getSubscriptionTypeName = (type: SubscriptionType) => {
    switch (type) {
      case 'basic':
        return 'Базовая';
      case 'premium':
        return 'Премиум';
      case 'vip':
        return 'VIP';
      default:
        return type;
    }
  };

  const getStatusColor = (status: SubscriptionStatus) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'expired':
        return 'error';
      case 'cancelled':
        return 'default';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: SubscriptionStatus) => {
    switch (status) {
      case 'active':
        return 'Активна';
      case 'expired':
        return 'Истекла';
      case 'cancelled':
        return 'Отменена';
      case 'pending':
        return 'Ожидает';
      default:
        return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
    }).format(amount);
  };

  const handleExport = () => {
    adminService.exportSubscriptions({
      status: statusFilter === 'all' ? undefined : statusFilter,
      type: typeFilter === 'all' ? undefined : typeFilter,
    });
  };

  if (isLoading) {
    return <Typography>Загрузка подписок...</Typography>;
  }

  // Данные для графика распределения подписок
  const subscriptionDistributionData = subscriptionStats ? {
    labels: subscriptionStats.distributionByType.map(item => getSubscriptionTypeName(item.type)),
    datasets: [
      {
        label: 'Количество подписок',
        data: subscriptionStats.distributionByType.map(item => item.count),
        backgroundColor: [
          '#36a2eb',
          '#ffce56',
          '#ff6384',
        ],
      },
    ],
  } : null;

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="h4" component="h1">
          Управление подписками
        </Typography>
        <Box className={styles.controls}>
          <IconButton onClick={() => refetch()} color="primary">
            <RefreshIcon />
          </IconButton>
          <Button
            startIcon={<ExportIcon />}
            onClick={handleExport}
            variant="outlined"
          >
            Экспорт
          </Button>
        </Box>
      </Box>

      {/* Статистика подписок */}
      {subscriptionStats && (
        <Grid container spacing={3} className={styles.statsGrid}>
          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent>
                <Box className={styles.statContent}>
                  <PersonIcon className={styles.statIcon} color="primary" />
                  <Box>
                    <Typography variant="h4" component="div">
                      {subscriptionStats.totalSubscriptions}
                    </Typography>
                    <Typography color="text.secondary">
                      Всего подписок
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent>
                <Box className={styles.statContent}>
                  <StarIcon className={styles.statIcon} color="success" />
                  <Box>
                    <Typography variant="h4" component="div">
                      {subscriptionStats.activeSubscriptions}
                    </Typography>
                    <Typography color="text.secondary">
                      Активных
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent>
                <Box className={styles.statContent}>
                  <DiamondIcon className={styles.statIcon} color="warning" />
                  <Box>
                    <Typography variant="h4" component="div">
                      {formatCurrency(subscriptionStats.monthlyRevenue)}
                    </Typography>
                    <Typography color="text.secondary">
                      Доход в месяц
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent>
                <Box className={styles.statContent}>
                  <RefreshIcon className={styles.statIcon} color="info" />
                  <Box>
                    <Typography variant="h4" component="div">
                      {subscriptionStats.renewalRate}%
                    </Typography>
                    <Typography color="text.secondary">
                      Продления
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Фильтры */}
      <Card className={styles.filtersCard}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Статус</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as SubscriptionStatus | 'all')}
                  label="Статус"
                >
                  <MenuItem value="all">Все</MenuItem>
                  <MenuItem value="active">Активные</MenuItem>
                  <MenuItem value="expired">Истекшие</MenuItem>
                  <MenuItem value="cancelled">Отмененные</MenuItem>
                  <MenuItem value="pending">Ожидающие</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Тип подписки</InputLabel>
                <Select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value as SubscriptionType | 'all')}
                  label="Тип подписки"
                >
                  <MenuItem value="all">Все</MenuItem>
                  <MenuItem value="basic">Базовая</MenuItem>
                  <MenuItem value="premium">Премиум</MenuItem>
                  <MenuItem value="vip">VIP</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary">
                Найдено: {subscriptionsData?.total || 0} подписок
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* График распределения */}
      {subscriptionDistributionData && (
        <Grid container spacing={3} className={styles.chartsGrid}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Распределение по типам подписок
                </Typography>
                <Box className={styles.chartContainer}>
                  <Doughnut data={subscriptionDistributionData} options={{ responsive: true, maintainAspectRatio: false }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Таблица подписок */}
      <TableContainer component={Paper} className={styles.tableContainer}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Пользователь</TableCell>
              <TableCell>Тип подписки</TableCell>
              <TableCell>Статус</TableCell>
              <TableCell>Начало</TableCell>
              <TableCell>Окончание</TableCell>
              <TableCell>Стоимость</TableCell>
              <TableCell>Действия</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {subscriptionsData?.subscriptions.map((subscription) => (
              <TableRow key={subscription.id} className={styles.subscriptionRow}>
                <TableCell>
                  <Box className={styles.userInfo}>
                    <Avatar src={subscription.user.avatar} className={styles.avatar}>
                      {subscription.user.firstName[0]}{subscription.user.lastName[0]}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2">
                        {subscription.user.firstName} {subscription.user.lastName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {subscription.user.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                
                <TableCell>
                  <Box className={styles.subscriptionType}>
                    {getSubscriptionTypeIcon(subscription.type)}
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      {getSubscriptionTypeName(subscription.type)}
                    </Typography>
                  </Box>
                </TableCell>
                
                <TableCell>
                  <Chip
                    label={getStatusText(subscription.status)}
                    color={getStatusColor(subscription.status) as any}
                    size="small"
                  />
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2">
                    {new Date(subscription.startDate).toLocaleDateString('ru-RU')}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2">
                    {new Date(subscription.endDate).toLocaleDateString('ru-RU')}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {formatCurrency(subscription.price)}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Box className={styles.actions}>
                    {subscription.status === 'active' && (
                      <>
                        <Tooltip title="Продлить">
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleSubscriptionAction(subscription, 'extend')}
                          >
                            <RefreshIcon />
                          </IconButton>
                        </Tooltip>
                        
                        <Tooltip title="Отменить">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleSubscriptionAction(subscription, 'cancel')}
                          >
                            <CancelIcon />
                          </IconButton>
                        </Tooltip>
                      </>
                    )}
                    
                    <Tooltip title="Просмотреть детали">
                      <IconButton
                        size="small"
                        onClick={() => window.open(`/admin/users/${subscription.user.id}`, '_blank')}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Пагинация */}
      {subscriptionsData && subscriptionsData.totalPages > 1 && (
        <Box className={styles.pagination}>
          <Pagination
            count={subscriptionsData.totalPages}
            page={page}
            onChange={(event, value) => setPage(value)}
            color="primary"
          />
        </Box>
      )}

      {/* Диалог управления подпиской */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'extend' && 'Продлить подписку'}
          {actionType === 'cancel' && 'Отменить подписку'}
          {actionType === 'upgrade' && 'Повысить подписку'}
        </DialogTitle>
        <DialogContent>
          {selectedSubscription && (
            <Box>
              <Typography variant="body1" gutterBottom>
                Пользователь: {selectedSubscription.user.firstName} {selectedSubscription.user.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Текущая подписка: {getSubscriptionTypeName(selectedSubscription.type)}
              </Typography>
              
              {actionType === 'extend' && (
                <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
                  <InputLabel>Продлить на</InputLabel>
                  <Select
                    value={extensionDays}
                    onChange={(e) => setExtensionDays(e.target.value)}
                    label="Продлить на"
                  >
                    <MenuItem value="7">7 дней</MenuItem>
                    <MenuItem value="30">30 дней</MenuItem>
                    <MenuItem value="90">90 дней</MenuItem>
                    <MenuItem value="365">1 год</MenuItem>
                  </Select>
                </FormControl>
              )}
              
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Причина"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                required
                sx={{ mt: 2 }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Отмена
          </Button>
          <Button
            onClick={handleConfirmAction}
            variant="contained"
            color={actionType === 'cancel' ? 'error' : 'primary'}
            disabled={!reason.trim() || manageSubscriptionMutation.isPending}
          >
            {manageSubscriptionMutation.isPending ? 'Обработка...' : 'Применить'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Уведомления об ошибках */}
      {manageSubscriptionMutation.error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          Ошибка: {manageSubscriptionMutation.error.message}
        </Alert>
      )}
    </Box>
  );
};

export default Subscriptions;
