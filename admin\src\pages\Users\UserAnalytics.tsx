import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Avatar,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Favorite as FavoriteIcon,
  Chat as ChatIcon,
  Visibility as ViewIcon,
  GetApp as ExportIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
} from 'chart.js';
import { adminService } from '../../../api/adminService';
import { UserAnalyticsData, TimeRange } from '../../../types/admin.types';
import styles from '../../../styles/admin/UserAnalytics.module.css';

// Регистрируем компоненты Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement,
  RadialLinearScale
);

const UserAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');

  // Загрузка аналитики пользователей
  const { data: analytics, isLoading } = useQuery({
    queryKey: ['admin', 'user-analytics', timeRange],
    queryFn: () => adminService.getUserAnalytics(timeRange),
  });

  const handleExport = () => {
    // Экспорт данных аналитики
    adminService.exportUserAnalytics(timeRange);
  };

  if (isLoading) {
    return <Typography>Загрузка аналитики...</Typography>;
  }

  if (!analytics) {
    return <Typography>Нет данных для отображения</Typography>;
  }

  // Данные для графика роста пользователей
  const userGrowthData = {
    labels: analytics.userGrowth.map(item => item.date),
    datasets: [
      {
        label: 'Новые пользователи',
        data: analytics.userGrowth.map(item => item.newUsers),
        borderColor: '#2196f3',
        backgroundColor: 'rgba(33, 150, 243, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Активные пользователи',
        data: analytics.userGrowth.map(item => item.activeUsers),
        borderColor: '#4caf50',
        backgroundColor: 'rgba(76, 175, 80, 0.1)',
        tension: 0.4,
      },
    ],
  };

  // Данные для графика активности по возрасту
  const ageDistributionData = {
    labels: analytics.ageDistribution.map(item => item.ageGroup),
    datasets: [
      {
        label: 'Количество пользователей',
        data: analytics.ageDistribution.map(item => item.count),
        backgroundColor: [
          '#ff6384',
          '#36a2eb',
          '#ffce56',
          '#4bc0c0',
          '#9966ff',
          '#ff9f40',
        ],
      },
    ],
  };

  // Данные для географического распределения
  const geoDistributionData = {
    labels: analytics.geoDistribution.map(item => item.city),
    datasets: [
      {
        label: 'Пользователи по городам',
        data: analytics.geoDistribution.map(item => item.count),
        backgroundColor: '#36a2eb',
      },
    ],
  };

  // Данные для активности пользователей
  const activityData = {
    labels: ['Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота', 'Воскресенье'],
    datasets: [
      {
        label: 'Активность по дням недели',
        data: analytics.weeklyActivity,
        backgroundColor: 'rgba(76, 175, 80, 0.6)',
        borderColor: '#4caf50',
        pointBackgroundColor: '#4caf50',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: '#4caf50',
      },
    ],
  };

  const radarOptions = {
    scales: {
      r: {
        angleLines: {
          display: false
        },
        suggestedMin: 0,
        suggestedMax: 100
      }
    }
  };

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="h4" component="h1">
          Аналитика пользователей
        </Typography>
        <Box className={styles.controls}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Период</InputLabel>
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as TimeRange)}
              label="Период"
            >
              <MenuItem value="7d">7 дней</MenuItem>
              <MenuItem value="30d">30 дней</MenuItem>
              <MenuItem value="90d">3 месяца</MenuItem>
              <MenuItem value="1y">1 год</MenuItem>
            </Select>
          </FormControl>
          <IconButton onClick={handleExport} color="primary">
            <ExportIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Основные метрики */}
      <Grid container spacing={3} className={styles.metricsGrid}>
        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.metricCard}>
            <CardContent>
              <Box className={styles.metricContent}>
                <PeopleIcon className={styles.metricIcon} color="primary" />
                <Box>
                  <Typography variant="h4" component="div">
                    {analytics.totalUsers.toLocaleString()}
                  </Typography>
                  <Typography color="text.secondary">
                    Всего пользователей
                  </Typography>
                  <Box className={styles.trend}>
                    {analytics.userGrowthPercent > 0 ? (
                      <TrendingUpIcon color="success" />
                    ) : (
                      <TrendingDownIcon color="error" />
                    )}
                    <Typography variant="body2" color={analytics.userGrowthPercent > 0 ? 'success.main' : 'error.main'}>
                      {Math.abs(analytics.userGrowthPercent)}%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.metricCard}>
            <CardContent>
              <Box className={styles.metricContent}>
                <PersonAddIcon className={styles.metricIcon} color="success" />
                <Box>
                  <Typography variant="h4" component="div">
                    {analytics.newUsersToday}
                  </Typography>
                  <Typography color="text.secondary">
                    Новых сегодня
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.metricCard}>
            <CardContent>
              <Box className={styles.metricContent}>
                <FavoriteIcon className={styles.metricIcon} color="error" />
                <Box>
                  <Typography variant="h4" component="div">
                    {analytics.totalMatches.toLocaleString()}
                  </Typography>
                  <Typography color="text.secondary">
                    Всего совпадений
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className={styles.metricCard}>
            <CardContent>
              <Box className={styles.metricContent}>
                <ChatIcon className={styles.metricIcon} color="info" />
                <Box>
                  <Typography variant="h4" component="div">
                    {analytics.activeChats}
                  </Typography>
                  <Typography color="text.secondary">
                    Активных чатов
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Графики */}
      <Grid container spacing={3} className={styles.chartsGrid}>
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Рост пользователей
              </Typography>
              <Box className={styles.chartContainer}>
                <Line data={userGrowthData} options={{ responsive: true, maintainAspectRatio: false }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Распределение по возрасту
              </Typography>
              <Box className={styles.chartContainer}>
                <Doughnut data={ageDistributionData} options={{ responsive: true, maintainAspectRatio: false }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                География пользователей
              </Typography>
              <Box className={styles.chartContainer}>
                <Bar data={geoDistributionData} options={{ responsive: true, maintainAspectRatio: false }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Активность по дням недели
              </Typography>
              <Box className={styles.chartContainer}>
                <Radar data={activityData} options={radarOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Топ пользователи */}
      <Card className={styles.topUsersCard}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Самые активные пользователи
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Пользователь</TableCell>
                  <TableCell>Совпадения</TableCell>
                  <TableCell>Сообщения</TableCell>
                  <TableCell>Активность</TableCell>
                  <TableCell>Действия</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {analytics.topUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box className={styles.userInfo}>
                        <Avatar src={user.avatar} className={styles.avatar}>
                          {user.firstName[0]}{user.lastName[0]}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2">
                            {user.firstName} {user.lastName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip label={user.matchesCount} color="primary" size="small" />
                    </TableCell>
                    <TableCell>
                      <Chip label={user.messagesCount} color="secondary" size="small" />
                    </TableCell>
                    <TableCell>
                      <Box className={styles.activityBar}>
                        <LinearProgress
                          variant="determinate"
                          value={user.activityScore}
                          className={styles.progressBar}
                        />
                        <Typography variant="caption">
                          {user.activityScore}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Просмотреть профиль">
                        <IconButton
                          size="small"
                          onClick={() => window.open(`/admin/users/${user.id}`, '_blank')}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default UserAnalytics;
