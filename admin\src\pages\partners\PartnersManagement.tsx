import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Avatar,
  TextField,
  MenuItem,
  Tabs,
  Tab,
  Rating,
  LinearProgress,
} from '@mui/material';
import {
  Visibility,
  Edit,
  Delete,
  Block,
  CheckCircle,
  Business,
  LocationOn,
  Phone,
  Email,
  Star,
  TrendingUp,
  People,
  Event,
  AttachMoney,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../api/adminApi';
import { Partner, PartnerApplication } from '../../types/admin.types';
import { formatDate, formatCurrency } from '../../utils/formatUtils';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`partners-tabpanel-${index}`}
      aria-labelledby={`partners-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const PartnersManagement: React.FC = () => {
  const queryClient = useQueryClient();
  
  const [tabValue, setTabValue] = useState(0);
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<PartnerApplication | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [applicationDialogOpen, setApplicationDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  const [filters, setFilters] = useState({
    status: 'all',
    category: 'all',
    search: '',
  });

  // Загрузка партнеров
  const {
    data: partnersData,
    isLoading: partnersLoading,
  } = useQuery({
    queryKey: ['admin', 'partners', filters],
    queryFn: () => adminApi.getPartners(filters),
  });

  // Загрузка заявок на партнерство
  const {
    data: applicationsData,
    isLoading: applicationsLoading,
  } = useQuery({
    queryKey: ['admin', 'partner-applications'],
    queryFn: () => adminApi.getPartnerApplications(),
  });

  // Мутация для одобрения/отклонения заявки
  const handleApplicationMutation = useMutation({
    mutationFn: ({ id, action, reason }: { id: string; action: 'approve' | 'reject'; reason?: string }) =>
      adminApi.handlePartnerApplication(id, action, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'partner-applications'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'partners'] });
      setApplicationDialogOpen(false);
    },
  });

  // Мутация для блокировки партнера
  const blockPartnerMutation = useMutation({
    mutationFn: (id: string) => adminApi.blockPartner(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'partners'] });
    },
  });

  // Мутация для удаления партнера
  const deletePartnerMutation = useMutation({
    mutationFn: (id: string) => adminApi.deletePartner(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'partners'] });
      setDeleteDialogOpen(false);
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleViewPartner = (partner: Partner) => {
    setSelectedPartner(partner);
    setViewDialogOpen(true);
  };

  const handleViewApplication = (application: PartnerApplication) => {
    setSelectedApplication(application);
    setApplicationDialogOpen(true);
  };

  const handleApproveApplication = (application: PartnerApplication) => {
    handleApplicationMutation.mutate({ id: application.id, action: 'approve' });
  };

  const handleRejectApplication = (application: PartnerApplication, reason: string) => {
    handleApplicationMutation.mutate({ id: application.id, action: 'reject', reason });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'blocked': return 'error';
      case 'pending': return 'warning';
      case 'suspended': return 'warning';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Активен';
      case 'blocked': return 'Заблокирован';
      case 'pending': return 'Ожидает';
      case 'suspended': return 'Приостановлен';
      default: return status;
    }
  };

  const renderPartnersTable = (partners: Partner[]) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Заведение</TableCell>
            <TableCell>Контакты</TableCell>
            <TableCell>Категория</TableCell>
            <TableCell>Рейтинг</TableCell>
            <TableCell>События</TableCell>
            <TableCell>Доход</TableCell>
            <TableCell>Статус</TableCell>
            <TableCell>Действия</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {partners.map((partner) => (
            <TableRow key={partner.id}>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar src={partner.logo} sx={{ mr: 2 }}>
                    <Business />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" fontWeight="bold">
                      {partner.name}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      <LocationOn sx={{ fontSize: 12, mr: 0.5 }} />
                      {partner.address}
                    </Typography>
                  </Box>
                </Box>
              </TableCell>
              
              <TableCell>
                <Box>
                  <Typography variant="body2">
                    <Phone sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                    {partner.phone}
                  </Typography>
                  <Typography variant="body2">
                    <Email sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                    {partner.email}
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Chip label={partner.category} size="small" />
              </TableCell>
              
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Rating value={partner.rating} readOnly size="small" />
                  <Typography variant="body2" sx={{ ml: 1 }}>
                    ({partner.reviewsCount})
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Event sx={{ fontSize: 16, mr: 0.5 }} />
                  <Typography variant="body2">
                    {partner.eventsCount}
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2" fontWeight="bold">
                  {formatCurrency(partner.totalRevenue)}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  за месяц
                </Typography>
              </TableCell>
              
              <TableCell>
                <Chip
                  label={getStatusLabel(partner.status)}
                  color={getStatusColor(partner.status) as any}
                  size="small"
                />
              </TableCell>
              
              <TableCell>
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  <IconButton
                    size="small"
                    onClick={() => handleViewPartner(partner)}
                  >
                    <Visibility />
                  </IconButton>
                  
                  <IconButton
                    size="small"
                    onClick={() => {/* navigate to edit */}}
                  >
                    <Edit />
                  </IconButton>
                  
                  {partner.status === 'active' && (
                    <IconButton
                      size="small"
                      color="warning"
                      onClick={() => blockPartnerMutation.mutate(partner.id)}
                    >
                      <Block />
                    </IconButton>
                  )}
                  
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => {
                      setSelectedPartner(partner);
                      setDeleteDialogOpen(true);
                    }}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderApplicationsTable = (applications: PartnerApplication[]) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Заведение</TableCell>
            <TableCell>Контакты</TableCell>
            <TableCell>Категория</TableCell>
            <TableCell>Дата подачи</TableCell>
            <TableCell>Статус</TableCell>
            <TableCell>Действия</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {applications.map((application) => (
            <TableRow key={application.id}>
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    {application.businessName}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    <LocationOn sx={{ fontSize: 12, mr: 0.5 }} />
                    {application.address}
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Box>
                  <Typography variant="body2">
                    {application.contactName}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    {application.email}
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Chip label={application.category} size="small" />
              </TableCell>
              
              <TableCell>
                <Typography variant="body2">
                  {formatDate(application.createdAt)}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Chip
                  label={getStatusLabel(application.status)}
                  color={getStatusColor(application.status) as any}
                  size="small"
                />
              </TableCell>
              
              <TableCell>
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => handleViewApplication(application)}
                  >
                    Просмотр
                  </Button>
                  
                  {application.status === 'pending' && (
                    <>
                      <Button
                        size="small"
                        variant="contained"
                        color="success"
                        onClick={() => handleApproveApplication(application)}
                      >
                        Одобрить
                      </Button>
                      
                      <Button
                        size="small"
                        variant="outlined"
                        color="error"
                        onClick={() => handleRejectApplication(application, 'Не соответствует требованиям')}
                      >
                        Отклонить
                      </Button>
                    </>
                  )}
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Управление партнерами
      </Typography>

      {/* Статистика */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Всего партнеров
                  </Typography>
                  <Typography variant="h4">
                    {partnersData?.total || 0}
                  </Typography>
                </Box>
                <Business sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Активные
                  </Typography>
                  <Typography variant="h4">
                    {partnersData?.active || 0}
                  </Typography>
                </Box>
                <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Новые заявки
                  </Typography>
                  <Typography variant="h4">
                    {applicationsData?.pending || 0}
                  </Typography>
                </Box>
                <Star sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Общий доход
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(partnersData?.totalRevenue || 0)}
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Фильтры */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Статус"
                value={filters.status}
                onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                fullWidth
                size="small"
              >
                <MenuItem value="all">Все</MenuItem>
                <MenuItem value="active">Активные</MenuItem>
                <MenuItem value="blocked">Заблокированные</MenuItem>
                <MenuItem value="pending">Ожидающие</MenuItem>
              </TextField>
            </Grid>
            
            <Grid item xs={12} sm={3}>
              <TextField
                select
                label="Категория"
                value={filters.category}
                onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                fullWidth
                size="small"
              >
                <MenuItem value="all">Все</MenuItem>
                <MenuItem value="restaurant">Рестораны</MenuItem>
                <MenuItem value="bar">Бары</MenuItem>
                <MenuItem value="cafe">Кафе</MenuItem>
                <MenuItem value="club">Клубы</MenuItem>
              </TextField>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                label="Поиск"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                fullWidth
                size="small"
                placeholder="Поиск по названию или адресу..."
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Вкладки */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Партнеры" />
            <Tab label="Заявки на партнерство" />
          </Tabs>
        </Box>

        {/* Список партнеров */}
        <TabPanel value={tabValue} index={0}>
          {partnersLoading ? (
            <LinearProgress />
          ) : partnersData?.items?.length ? (
            renderPartnersTable(partnersData.items)
          ) : (
            <Alert severity="info">
              Партнеры не найдены
            </Alert>
          )}
        </TabPanel>

        {/* Заявки на партнерство */}
        <TabPanel value={tabValue} index={1}>
          {applicationsLoading ? (
            <LinearProgress />
          ) : applicationsData?.items?.length ? (
            renderApplicationsTable(applicationsData.items)
          ) : (
            <Alert severity="info">
              Нет заявок на партнерство
            </Alert>
          )}
        </TabPanel>
      </Card>

      {/* Диалог просмотра партнера */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Информация о партнере</DialogTitle>
        <DialogContent>
          {selectedPartner && (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Название
                </Typography>
                <Typography variant="body2" gutterBottom>
                  {selectedPartner.name}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Категория
                </Typography>
                <Typography variant="body2" gutterBottom>
                  {selectedPartner.category}
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Адрес
                </Typography>
                <Typography variant="body2" gutterBottom>
                  {selectedPartner.address}
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Описание
                </Typography>
                <Typography variant="body2" gutterBottom>
                  {selectedPartner.description}
                </Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Закрыть</Button>
        </DialogActions>
      </Dialog>

      {/* Диалог удаления */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Удалить партнера</DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            Это действие нельзя отменить!
          </Alert>
          <Typography>
            Вы уверены, что хотите удалить партнера {selectedPartner?.name}?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Отмена</Button>
          <Button
            onClick={() => selectedPartner && deletePartnerMutation.mutate(selectedPartner.id)}
            color="error"
            variant="contained"
          >
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PartnersManagement;
